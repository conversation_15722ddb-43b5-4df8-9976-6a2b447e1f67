﻿using System;
using Medusa.Service.Ocelot.ApiHost.Core.Enums;

namespace Medusa.Service.Ocelot.ApiHost.Application.Dtos
{
    /// <summary>
    /// 映射结构
    /// </summary>
    public class MappingSchemaDto
    {
        /// <summary>
        /// 映射Id
        /// </summary>
        public Guid? MappingId { get; set; }

        /// <summary>
        /// 映射名称
        /// </summary>
        public string MappingName { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 父映射Id
        /// </summary>
        public Guid? UpperMappingId { get; set; }

        /// <summary>
        /// 排序位置
        /// </summary>
        public int? OrderIndex { get; set; }

        /// <summary>
        /// 映射源
        /// </summary>
        public MappingDataSource? DataSource { get; set; }

        /// <summary>
        /// 映射源值
        /// </summary>
        public string DataSourceValue { get; set; }

        /// <summary>
        /// SchemaId
        /// </summary>
        public Guid? SchemaId { get; set; }

        /// <summary>
        /// Schema Tree Path
        /// </summary>
        public string SchemaTreePath { get; set; }
    }
}
