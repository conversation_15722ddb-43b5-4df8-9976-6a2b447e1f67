using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration
{
    /// <summary>
    /// Api组合请求参数示例
    /// </summary>
    [EntityTable("ApiGroupRequestSample", "Api组合请求参数示例")]
    public class ApiGroupRequestSample
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? InnerId { get; set; }

        /// <summary>
        /// 请求参数示例类型
        /// </summary>
        [EntityColumn(ColumnDescription = "请求参数示例类型", ColumnDataType = "varchar(50)")]
        public string RequestSampleType { get; set; }

        /// <summary>
        /// 样例值
        /// </summary>
        [EntityColumn(ColumnDescription = "样例值", ColumnDataType = "varchar(max),text")]
        public string DataValue { get; set; }

        /// <summary>
        /// Api组合主键Id
        /// </summary>
        [EntityColumn(ColumnDescription = "Api组合主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ApiGroupId { get; set; }
    }
}