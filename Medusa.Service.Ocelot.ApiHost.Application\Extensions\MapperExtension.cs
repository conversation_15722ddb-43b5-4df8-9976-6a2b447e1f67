﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using Medusa.Service.Ocelot.ApiHost.Application.Dtos;
using Medusa.Service.Ocelot.ApiHost.Application.OcelotApi.Dtos;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration;
using Medusa.Service.Ocelot.ApiHost.Core.Enums;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Medusa.Service.Ocelot.ApiHost.Application.Extensions
{
    /// <summary>
    /// 映射扩展类
    /// </summary>
    public static class MapperExtension
    {
        /// <summary>
        /// Entity复制
        /// </summary>
        /// <typeparam name="TIn">入参实体</typeparam>
        /// <typeparam name="TOut">出参实体</typeparam>
        /// <param name="inEntity">入参实体</param>
        /// <param name="outEntity">出参实体</param>
        /// <param name="ignorePoperties">忽略属性</param>
        /// <returns>返回出参实体</returns>
        public static TOut EntityCopy<TIn, TOut>(this TIn inEntity, TOut outEntity, List<string> ignorePoperties)
        {
            if (ignorePoperties == null)
            {
                ignorePoperties = new List<string>();
            }

            var inPoperties = inEntity.GetType().GetProperties();
            var outPoperties = outEntity.GetType().GetProperties();
            foreach (var item in outPoperties)
            {
                if (!ignorePoperties.Contains(item.Name) && item.CanWrite)
                {
                    if (inPoperties.Any(x => x.Name == item.Name && x.PropertyType == item.PropertyType))
                    {
                        var inValue = inPoperties.First(x => x.Name == item.Name).GetValue(inEntity, null);
                        item.SetValue(outEntity, inValue, null);
                    }
                }
            }

            return outEntity;
        }

        /// <summary>
        /// 接口返回数据转成接口定义的映射结构
        /// </summary>
        /// <param name="responseSchemas"></param>
        /// <param name="apiValue"></param>
        /// <param name="isMapper">返回结果是否有映射</param>
        /// <param name="upperSchemaId"></param>
        /// <param name="parentValue"></param>
        /// <returns></returns>
        public static JToken ToMappingResponse(this List<ApiResponseSchemaDto> responseSchemas, JToken apiValue, bool isMapper, Guid? upperSchemaId = null, JToken parentValue = null)
        {
            JToken rootObj = new JObject();
            if (!upperSchemaId.HasValue)
            {
                var rootSchema = responseSchemas.Find(r => !r.UpperSchemaId.HasValue);

                if (isMapper)
                {
                    if (!string.IsNullOrEmpty(rootSchema.DataSource))
                    {
                        JToken value = null;
                        switch (rootSchema.DataSource.ToOurEnum<MappingDataSource>())
                        {
                            case MappingDataSource.FixedValue:
                                value = JToken.FromObject(rootSchema.FixedValue);
                                break;
                            case MappingDataSource.ResultSchema:
                            case MappingDataSource.RequestBody:
                                value = rootSchema.SchemaPaths.Aggregate(apiValue, (current, schemaName) => current[schemaName]);
                                break;
                        }

                        switch (rootSchema.DataType)
                        {
                            case "object":
                                if (responseSchemas.Any(x => x.UpperSchemaId == rootSchema.SchemaId))
                                {
                                    rootObj = responseSchemas.ToMappingResponse(apiValue, isMapper, rootSchema.SchemaId, value);
                                }
                                else
                                {
                                    rootObj = JToken.Parse("{}");
                                }

                                break;
                            case "array":
                                rootObj = value;
                                if (responseSchemas.Any(x => x.UpperSchemaId == rootSchema.SchemaId))
                                {
                                    var arrayChildren = responseSchemas.Where(x => x.UpperSchemaId == rootSchema.SchemaId).ToList();

                                    rootObj = arrayChildren.ToMappingResponseArray(value);
                                }

                                break;
                            default:
                                rootObj = value;
                                break;
                        }

                        return rootObj;
                    }
                }

                if (responseSchemas.Any(r => r.UpperSchemaId == upperSchemaId))
                {
                    rootObj = responseSchemas.ToMappingResponse(apiValue, isMapper, rootSchema.SchemaId);
                }
            }
            else
            {
                var children = responseSchemas.Where(r => r.UpperSchemaId == upperSchemaId).ToList();
                children.ForEach(r =>
                {
                    JToken value = null;
                    if (isMapper)
                    {
                        if (!string.IsNullOrEmpty(r.DataSource))
                        {
                            switch (r.DataSource.ToOurEnum<MappingDataSource>())
                            {
                                case MappingDataSource.FixedValue:
                                    value = JToken.FromObject(r.FixedValue);
                                    break;
                                case MappingDataSource.ResultSchema:
                                case MappingDataSource.RequestBody:
                                    value = r.SchemaPaths.Aggregate(apiValue, (current, schemaName) => current[schemaName]);
                                    break;
                            }
                        }
                    }
                    else
                    {
                        value = (parentValue ?? apiValue)[r.SchemaName];
                    }

                    switch (r.DataType)
                    {
                        case "number":
                            if (value?.Type == JTokenType.Integer)
                            {
                                ((JObject)rootObj).Add(r.SchemaName, value?.ToOurString().ToOurInt());
                            }
                            else
                            {
                                var numberData = value?.ToOurString();
                                var isInt = numberData.RemoveZero().IsInt();
                                if (isInt)
                                {
                                    ((JObject)rootObj).Add(r.SchemaName, numberData.ToOurInt());
                                }
                                else
                                {
                                    ((JObject)rootObj).Add(r.SchemaName, numberData.ToOurDecimal());
                                }
                            }

                            break;
                        case "boolean":
                            ((JObject)rootObj).Add(r.SchemaName, value?.ToOurString().ToOurBoolean());
                            break;
                        case "date":
                            ((JObject)rootObj).Add(r.SchemaName, value?.ToOurString().ToOurDateTime());
                            break;
                        case "object":
                            if (responseSchemas.Any(x => x.UpperSchemaId == r.SchemaId))
                            {
                                ((JObject)rootObj).Add(r.SchemaName, responseSchemas.ToMappingResponse(apiValue, isMapper, r.SchemaId, value));
                            }
                            else
                            {
                                ((JObject)rootObj).Add(r.SchemaName, JToken.Parse("{}"));
                            }

                            break;
                        case "array":
                            if (responseSchemas.Any(x => x.UpperSchemaId == r.SchemaId))
                            {
                                var arrayChildren = responseSchemas.Where(x => x.UpperSchemaId == r.SchemaId).ToList();
                                if (value != null && value.Count() > 0)
                                {
                                    // 判断结构是否一致
                                    var names = ((JObject)value[0]).Properties().Select(x => x.Name).ToList();
                                    if (arrayChildren.Count(x => !names.Contains(x.SchemaName)) != arrayChildren.Count)
                                    {
                                        var filterFields = names.Where(x => !arrayChildren.Any(z => z.SchemaName == x)).ToList();
                                        foreach (JObject item in value)
                                        {
                                            filterFields.ForEach(x =>
                                            {
                                                if (item.ContainsKey(x))
                                                {
                                                    item.Remove(x);
                                                }
                                            });
                                        }

                                        ((JObject)rootObj).Add(r.SchemaName, value);
                                    }
                                    else
                                    {
                                        ((JObject)rootObj).Add(r.SchemaName, value);
                                    }
                                }
                                else
                                {
                                    ((JObject)rootObj).Add(r.SchemaName, value);
                                }
                            }
                            else
                            {
                                ((JObject)rootObj).Add(r.SchemaName, value);
                            }

                            break;
                        default:
                            ((JObject)rootObj).Add(r.SchemaName, value?.ToOurString());
                            break;
                    }
                });
            }

            return rootObj;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="arrayChildren">定义的结构</param>
        /// <param name="arrayValue">原始数据</param>
        /// <returns></returns>
        private static JToken ToMappingResponseArray(this List<ApiResponseSchemaDto> arrayChildren, JToken arrayValue)
        {
            if (arrayValue != null && arrayValue.Count() > 0)
            {
                // 获取到的结构
                var getSchemas = ((JObject)arrayValue[0]).Properties().Select(x => new { Name = x.Name, Type = (x.Value.Type == JTokenType.Integer || x.Value.Type == JTokenType.Float) ? "NUMBER" : x.Value.Type.ToString().ToUpper() }).ToList();
                var names = getSchemas.Select(x => x.Name).ToList();

                // 判断结构是否一致
                var schemaEquals = arrayChildren.Count(x => !names.Contains(x.SchemaName)) != arrayChildren.Count;

                // 判断结构类型是否一致
                var schemaTypeEquals = arrayChildren.Count(x => getSchemas.Any(n => n.Name == x.SchemaName && x.DataType.ToUpper() == n.Type)) != arrayChildren.Count;

                if (schemaEquals || schemaTypeEquals)
                {
                    var filterFields = names.Where(x => !arrayChildren.Any(z => z.SchemaName == x)).ToList();
                    var changeFields = arrayChildren.Where(x => !getSchemas.Any(n => n.Name == x.SchemaName && x.DataType.ToUpper() == n.Type)).ToList();

                    foreach (JObject item in arrayValue)
                    {
                        // 过滤每个对象多余的属性
                        if (schemaEquals)
                        {
                            filterFields.ForEach(x =>
                            {
                                if (item.ContainsKey(x))
                                {
                                    item.Remove(x);
                                }
                            });
                        }

                        if (schemaTypeEquals)
                        {
                            changeFields.ForEach(x =>
                            {
                                if (item.ContainsKey(x.SchemaName))
                                {
                                    switch (x.DataType)
                                    {
                                        case "number":
                                            if (item?.Type == JTokenType.Integer)
                                            {
                                                item[x.SchemaName] = item[x.SchemaName]?.ToOurString().ToOurInt();
                                            }
                                            else
                                            {
                                                var numberData = item[x.SchemaName]?.ToOurString();
                                                var isInt = numberData.IsInt();
                                                if (isInt)
                                                {
                                                    item[x.SchemaName] = numberData.ToOurInt();
                                                }
                                                else
                                                {
                                                    item[x.SchemaName] = numberData.ToOurDecimal();
                                                }
                                            }

                                            break;
                                        case "boolean":
                                            item[x.SchemaName] = item[x.SchemaName]?.ToOurString().ToOurBoolean();
                                            break;
                                        case "date":
                                            item[x.SchemaName] = item[x.SchemaName]?.ToOurString().ToOurDateTime();
                                            break;
                                        case "string":
                                            item[x.SchemaName] = item[x.SchemaName]?.ToOurString();
                                            break;
                                    }
                                }
                            });
                        }
                    }

                    return arrayValue;
                }
            }

            return arrayValue;
        }

        private static bool IsInt(this string number)
        {
            if (string.IsNullOrEmpty(number))
            {
                return false;
            }

            // 判断是否是小数
            if (number.IndexOf(".") > -1 && number.Split('.').Length == 2)
            {
                var secondPart = number.Split('.')[1].TrimEnd('0');
                if (secondPart == string.Empty)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }

            return true;
        }

        private static string RemoveZero(this string number)
        {
            if (string.IsNullOrEmpty(number))
            {
                return string.Empty;
            }

            // 判断是否是小数
            if (number.IndexOf(".") > -1 && number.Split('.').Length == 2)
            {
                var firstPart = number.Split('.')[0];
                var secondPart = number.Split('.')[1].TrimEnd('0');
                if (secondPart == string.Empty)
                {
                    number = firstPart;
                }
                else
                {
                    number = firstPart + "." + secondPart;
                }
            }

            return number;
        }
    }
}
