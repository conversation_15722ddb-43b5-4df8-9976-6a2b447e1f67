﻿using System;
using System.Collections.Generic;

namespace Medusa.Service.Ocelot.ApiHost.Application.OcelotApi.Dtos
{
    /// <summary>
    /// ApiResponseSchemaDto
    /// </summary>
    public class ApiResponseSchemaDto
    {
        /// <summary>
        /// SchemaId
        /// </summary>
        public Guid? SchemaId { get; set; }

        /// <summary>
        /// SchemaName
        /// </summary>
        public string SchemaName { get; set; }

        /// <summary>
        /// DataType
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// Required
        /// </summary>
        public bool? Required { get; set; }

        /// <summary>
        /// Description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// UpperSchemaId
        /// </summary>
        public Guid? UpperSchemaId { get; set; }

        /// <summary>
        /// DataSource
        /// </summary>
        public string DataSource { get; set; }

        /// <summary>
        /// Fixed Value
        /// </summary>
        public string FixedValue { get; set; }

        /// <summary>
        /// Schema Paths
        /// </summary>
        public List<string> SchemaPaths { get; set; }
    }
}
