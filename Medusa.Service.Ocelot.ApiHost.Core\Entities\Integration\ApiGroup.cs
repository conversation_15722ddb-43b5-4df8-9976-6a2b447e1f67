using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration
{
    /// <summary>
    /// ApiGroups
    /// </summary>
    [EntityTable("ApiGroups", "Api组合")]
    public class ApiGroup
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ApiGroupId { get; set; }

        /// <summary>
        /// 组合名称
        /// </summary>
        [EntityColumn(ColumnDescription = "组合名称", ColumnDataType = "varchar(200)")]
        public string ApiGroupName { get; set; }

        /// <summary>
        /// 请求方式
        /// </summary>
        [EntityColumn(ColumnDescription = "请求方式", ColumnDataType = "varchar(200)")]
        public string RequestMethod { get; set; }

        /// <summary>
        /// 请求地址
        /// </summary>
        [EntityColumn(ColumnDescription = "请求地址", ColumnDataType = "varchar(200)")]
        public string RequestPath { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [EntityColumn(ColumnDescription = "描述", ColumnDataType = "varchar(1000)")]
        public string Description { get; set; }

        /// <summary>
        /// 当前版本号
        /// </summary>
        [EntityColumn(ColumnDescription = "当前版本号", ColumnDataType = "varchar(50)")]
        public string CurrentVersionNo { get; set; }

        /// <summary>
        /// 状态 1 有效 0 无效
        /// </summary>
        [EntityColumn(ColumnDescription = "状态 1 有效 0 无效")]
        public int? Status { get; set; }

        /// <summary>
        /// 发布状态 1 已发布 0 已下线 -1 未发布
        /// </summary>
        [EntityColumn(ColumnDescription = "发布状态 1 已发布 0 已下线 -1 未发布")]
        public int? PublishStatus { get; set; }

        /// <summary>
        /// 创建者Id
        /// </summary>
        [EntityColumn(ColumnDescription = "创建者Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? CreatorId { get; set; }

        /// <summary>
        /// 创建者名称
        /// </summary>
        [EntityColumn(ColumnDescription = "创建者名称", ColumnDataType = "varchar(50)")]
        public string CreatorName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [EntityColumn(ColumnDescription = "创建时间", ColumnDataType = "datetime(3),datetime")]
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 最后修改人Id
        /// </summary>
        [EntityColumn(ColumnDescription = "最后修改人Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ModifierId { get; set; }

        /// <summary>
        /// 最后修改人名称
        /// </summary>
        [EntityColumn(ColumnDescription = "最后修改人名称", ColumnDataType = "varchar(50)")]
        public string ModifierName { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [EntityColumn(ColumnDescription = "最后修改时间", ColumnDataType = "datetime(3),datetime")]
        public DateTime? ModifyDate { get; set; }
    }
}
