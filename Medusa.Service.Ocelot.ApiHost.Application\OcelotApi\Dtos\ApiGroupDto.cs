using System.Collections.Generic;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Application.OcelotApi.Dtos
{
    /// <summary>
    /// Api组合扩展实体
    /// </summary>
    public class ApiGroupDto : ApiGroup
    {
        /// <summary>
        /// 请求参数集合
        /// </summary>
        [EntityColumn(IsIgnore = true)]
        public List<ApiGroupRequestParam> RequestParams { get; set; }

        /// <summary>
        /// 组合项集合
        /// </summary>
        [EntityColumn(IsIgnore = true)]
        public List<ApiGroupItemDto> GroupItems { get; set; }
        
        /// <summary>
        /// 请求结果字段映射
        /// </summary>
        [EntityColumn(IsIgnore = true)]
        public List<ApiGroupResultMapping> ResultMappings { get; set; }
    }
}