﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Application.Dtos
{
    /// <summary>
    /// 
    /// </summary>
    [Serializable]
    public class MapperPathDto
    {
        /// <summary>
        /// 
        /// </summary>
        public MapperPathDto()
        {
            ChildMapper = new Dictionary<string, MapperPathDto>();
            MapperSelf = new Dictionary<string, MapperPathDto>();
        }

        /// <summary>
        /// 映射路径，多级路径以“.”符号拼接
        /// </summary>
        public string MapperPath { get; set; }

        /// <summary>
        /// 映射类型
        /// </summary>
        public Type MapperType { get; set; }

        /// <summary>
        /// 映射默认值
        /// </summary>
        public string MapperDefaultValue { get; set; }

        /// <summary>
        /// 结构本身映射(只对对象生效)
        /// </summary>
        public bool IsMapperSelf { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Dictionary<string, MapperPathDto> MapperSelf { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Dictionary<string, MapperPathDto> ChildMapper { get; set; }
    }
}
