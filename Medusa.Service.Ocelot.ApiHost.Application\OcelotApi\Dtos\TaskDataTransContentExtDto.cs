﻿using Medusa.Service.Ocelot.ApiHost.Core.Dtos;

namespace Medusa.Service.Ocelot.ApiHost.Application.OcelotApi.Dtos
{
    /// <summary>
    /// TaskDataTransContentExtDto
    /// </summary>
    public class TaskDataTransContentExtDto : TaskDataTransContentDto
    {
        /// <summary>
        /// 是否执行分发
        /// </summary>
        public bool ExecDistribution { get; set; }

        /// <summary>
        /// 分页分发
        /// </summary>
        public bool PaginationDistribution { get; set; }        

        /// <summary>
        /// 
        /// </summary>
        public bool SaveReceiveDataToMongoDbResult { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string MongoDbSaveErrorMsg { get; set; }
    }
}
