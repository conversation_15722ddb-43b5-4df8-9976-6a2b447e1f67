﻿using Medusa.Service.Ocelot.ApiHost.Application.OcelotApi;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.SDK.ApiRequest;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Medusa.Service.Ocelot.ApiHost.Application.Dtos;
using Medusa.Service.Ocelot.ApiHost.Application.OcelotApi.Dtos;
using Medusa.Service.Ocelot.ApiHost.Core.Dto.Ocelot;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Medusa.Service.Ocelot.ApiHost.Entrance.Extensions
{
    /// <summary>
    /// Api组合代理
    /// </summary>
    public class OcelotApiGroupsDistributor : DelegatingHandler
    {
        private readonly IOcelotApiSerivce _ocelotApiService;
        private readonly IRequestConnector _requestConnector;
        private readonly JObject _appSettings;
        private static DateTime _ocelotTokenExpiresTime;
        private static string _ocelotToken;
        
        /// <summary>
        /// Ocelot Token
        /// </summary>
        private static string OcelotToken => DateTime.Now < _ocelotTokenExpiresTime ? _ocelotToken : string.Empty;

        /// <summary>
        /// OcelotApiGroupsDistributor
        /// </summary>
        /// <param name="serviceProvider"></param>
        public OcelotApiGroupsDistributor(IServiceProvider serviceProvider)
        {
            _ocelotApiService = serviceProvider.GetService<IOcelotApiSerivce>();
            _requestConnector = serviceProvider.GetService<IRequestConnector>();
            var memoryCache = serviceProvider.GetService<IMemoryCache>();
            _appSettings = memoryCache.Get<JObject>("AppSettings");
        }

        /// <summary>
        /// 重构Send
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            var apiToken = request.Headers.GetValues("api-token").ToList().FirstOrDefault();
            var apiGroupDto = _ocelotApiService.GetApiGroup(apiToken);
            
            if (apiGroupDto == null)
            {
                throw new Exception("没有找到对应的Api组合");
            }
            
            // 获取请求参数， Key是参数名称
            var requestParams = new Dictionary<string, object>(request.Content.ReadAsAsync<Dictionary<string, object>>(cancellationToken).Result, StringComparer.OrdinalIgnoreCase);
            
            // Api执行结果，Key是ApiGroupItemId
            var apiResult = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
            
            // 循环组合内的Api
            foreach (var groupItem in apiGroupDto.GroupItems)
            {
                // 参数匹配
                var apiParams = new Dictionary<string, object>();
                if (groupItem.RequestParamModule.Trim().Equals("Mapping", StringComparison.OrdinalIgnoreCase))
                {
                    // 参数映射
                    foreach (var mapping in groupItem.ParamMappings)
                    {
                        switch (mapping.MappingType)
                        {
                            case "ParamSchema":
                                if (requestParams.ContainsKey(mapping.MappingSchemaName))
                                {
                                    apiParams.Add(mapping.ParamName, requestParams[mapping.MappingSchemaName]);      
                                }
                                break;
                            case "ResultSchema":
                                if (apiResult.ContainsKey(mapping.ItemId.ToString()))
                                {
                                    // 如果映射root节点
                                    if (mapping.MappingSchemaName.Equals("root", StringComparison.OrdinalIgnoreCase))
                                    {
                                        apiParams.Add(mapping.ParamName, apiResult[mapping.ItemId.ToString()]);      
                                    }
                                    else
                                    {
                                        var apiBaseId = apiGroupDto.GroupItems
                                            .Find(d => d.ApiGroupItemId == mapping.ItemId)!.ApiBaseId;
                                        var data = FindResponseMappingData(apiBaseId, mapping.MappingSchemaId,
                                            apiResult[mapping.ItemId.ToString()]);
                                        if (data != null)
                                        {
                                            apiParams.Add(mapping.ParamName, data);   
                                        }
                                    }
                                }
                                break;
                            case "FixedValue":
                                apiParams.Add(mapping.ParamName, mapping.ParamValue);
                                break;
                        }
                    }
                }
                else
                {
                    // 参数透传，匹配参数名称一致
                    var schemas = _ocelotApiService.GetApiRequestBodySchemas(groupItem.ApiBaseId);
                    foreach (var schema in schemas)
                    {
                        if (schema.SchemaName.Trim().Equals("root", StringComparison.OrdinalIgnoreCase))
                        {
                            continue;
                        }

                        if (requestParams.ContainsKey(schema.SchemaName))
                        {
                            apiParams.Add(schema.SchemaName, requestParams[schema.SchemaName]);   
                        }
                    }
                }

                var httpClient = new HttpClient();
                // 获取ocelot授权token
                AttachOcelotToken(httpClient, cancellationToken);
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, new Uri($"{_appSettings["OcelotSettings"]?["Host"]}{groupItem.RequestPath}"));
                requestMessage.Content = new StringContent(JsonConvert.SerializeObject(apiParams), Encoding.UTF8, "application/json");
                var httpResponse = httpClient.SendAsync(requestMessage, cancellationToken).ConfigureAwait(false).GetAwaiter().GetResult();
                var content = httpResponse.Content.ReadAsStringAsync().GetAwaiter().GetResult();
                var result = JsonConvert.DeserializeObject<OcelotResponseDto>(content);
                if (result != null)
                {
                    apiResult.Add(groupItem.ApiGroupItemId.ToString(), result.Data);
                }
            }
            
            // 处理结果映射
            var rootObject = new JObject();
            var responseDto = new OcelotResponseDto() { ErrorCode = "0", Data = rootObject };
            var rootSchema = apiGroupDto.ResultMappings.Find(d => d.SchemaName == "root");
            if (rootSchema != null)
            {
                // 如果root节点进行映射，则直接赋值返回
                if (!string.IsNullOrEmpty(rootSchema.MappingType))
                {
                    switch (rootSchema.MappingType)
                    {
                        case "ParamSchema":
                            responseDto.Data = requestParams[rootSchema.MappingSchemaName];
                            break;
                        case "ResultSchema":
                            if (rootSchema.MappingSchemaName == "root")
                            {
                                responseDto.Data = apiResult[rootSchema.ItemId.ToString()];
                            }
                            else
                            {
                                var apiBaseId = apiGroupDto.GroupItems.Find(d => d.ApiGroupItemId == rootSchema.ItemId)?
                                    .ApiBaseId;
                                var data = FindResponseMappingData(apiBaseId, rootSchema.MappingSchemaId,
                                    apiResult[rootSchema.ItemId.ToString()]);
                                if (data != null)
                                {
                                    responseDto.Data = data;
                                }
                            }
                            break;
                        case "FixedValue":
                            responseDto.Data = rootSchema.ParamValue;
                            break;
                    }
                    return await OcelotExtension.ResponseTaskAsync(responseDto);
                }

                var childrenMappings =
                    apiGroupDto.ResultMappings.Where(d => d.UpperMappingId == rootSchema.MappingId).ToList();
                foreach (var mapping in childrenMappings)
                {
                    switch (mapping.MappingType)
                    {
                        case "ParamSchema":
                            rootObject.Add(mapping.SchemaName, JToken.FromObject(requestParams[mapping.MappingSchemaName]));
                            break;
                        case "ResultSchema":
                            if (mapping.MappingSchemaName == "root")
                            {
                                rootObject.Add(mapping.SchemaName, JToken.FromObject(apiResult[mapping.ItemId.ToString()]));
                            }
                            else
                            {
                                var apiBaseId = apiGroupDto.GroupItems.Find(d => d.ApiGroupItemId == mapping.ItemId)?
                                    .ApiBaseId;
                                var data = FindResponseMappingData(apiBaseId, mapping.MappingSchemaId,
                                    apiResult[mapping.ItemId.ToString()]);
                                if (data != null)
                                {
                                    rootObject.Add(mapping.SchemaName, JToken.FromObject(data));
                                }
                            }
                            break;
                        case "FixedValue":
                            rootObject.Add(mapping.SchemaName, JToken.FromObject(mapping.ParamValue));
                            break;
                        default:
                            if (mapping.DataType == "object")
                            {
                                rootObject.Add(mapping.SchemaName, JToken.Parse("{}"));
                            }
                            break;
                    }

                    if (mapping.DataType == "object")
                    {
                        FillChildrenResultToRoot((JObject)rootObject[mapping.SchemaName], mapping, apiGroupDto,
                            requestParams, apiResult);
                    }
                }
            }

            responseDto.Data = rootObject;
            return await OcelotExtension.ResponseTaskAsync(responseDto);
        }

        private void FillChildrenResultToRoot(JObject currentObject, ApiGroupResultMapping currentMapping,
            ApiGroupDto apiGroupDto, Dictionary<string, object> requestParams, Dictionary<string, object> apiResult)
        {
            var childrenMappings = apiGroupDto.ResultMappings.Where(d => d.UpperMappingId == currentMapping.MappingId).ToList();
            foreach (var mapping in childrenMappings)
            {
                switch (mapping.MappingType)
                {
                    case "ParamSchema":
                        currentObject.Add(mapping.SchemaName,
                            JToken.FromObject(requestParams[mapping.MappingSchemaName]));
                        break;
                    case "ResultSchema":
                        if (mapping.MappingSchemaName == "root")
                        {
                            currentObject.Add(mapping.SchemaName, JToken.FromObject(apiResult[mapping.ItemId.ToString()]));
                        }
                        else
                        {
                            var apiBaseId = apiGroupDto.GroupItems.Find(d => d.ApiGroupItemId == mapping.ItemId)?
                                .ApiBaseId;
                            var data = FindResponseMappingData(apiBaseId, mapping.MappingSchemaId,
                                apiResult[mapping.ItemId.ToString()]);
                            if (data != null)
                            {
                                currentObject.Add(mapping.SchemaName, JToken.FromObject(data));
                            }
                        }

                        break;
                    case "FixedValue":
                        currentObject.Add(mapping.SchemaName, JToken.FromObject(mapping.ParamValue));
                        break;
                    default:
                        if (mapping.DataType == "object")
                        {
                            currentObject.Add(mapping.SchemaName, JToken.Parse("{}"));
                        }
                        break;
                }

                if (mapping.DataType == "object")
                {
                    FillChildrenResultToRoot((JObject)currentObject[mapping.SchemaName], mapping, apiGroupDto,
                        requestParams, apiResult);   
                }
            }
        }

        private object FindResponseMappingData(Guid? apiBaseId, Guid? schemaId, object apiResult)
        {
            var apiResultSchemas = _ocelotApiService.GetApiResponseSchemas(apiBaseId);
            var resultSchema =
                apiResultSchemas.Find(d => d.SchemaId == schemaId);
            if (resultSchema != null)
            {
                var schemaPaths = new List<string> { resultSchema.SchemaName };
                var upperSchemaId = resultSchema.UpperSchemaId;
                while (upperSchemaId.HasValue)
                {
                    var upperSchema =
                        apiResultSchemas.Find(d => d.SchemaId == upperSchemaId);
                    if (upperSchema != null)
                    {
                        if (!upperSchema.SchemaName.Equals("root",
                                StringComparison.OrdinalIgnoreCase))
                        {
                            schemaPaths.Add(upperSchema.SchemaName);
                        }
                    }

                    upperSchemaId = upperSchema?.UpperSchemaId;
                }

                schemaPaths.Reverse();

                var obj = JToken.FromObject(apiResult);
                obj = schemaPaths.Aggregate(obj,
                    (current, schemaName) => current[schemaName]);
                return obj;
            }

            return null;
        }

        private void AttachOcelotToken(HttpClient baseClient, CancellationToken cancelToken)
        {
            if (string.IsNullOrEmpty(OcelotToken))
            {
                var requestParam = new Dictionary<string, string>
                {
                    { "client_id",  _appSettings["OcelotAuthSettings"]?["UserName"]?.ToString()},
                    { "client_secret", _appSettings["OcelotAuthSettings"]?["Password"]?.ToString() },
                    { "grant_type", _appSettings["OcelotAuthSettings"]?["GrantType"]?.ToString() }
                };

                var client = new HttpClient();
                var formStr = string.Join('&', requestParam.Select(kv => $"{kv.Key}={WebUtility.UrlEncode(kv.Value)}"));
                var formCtx = new StringContent(formStr, Encoding.UTF8, "application/x-www-form-urlencoded");
                var request = new HttpRequestMessage(HttpMethod.Post, _appSettings["OcelotAuthSettings"]?["Host"]?.ToString());
                request.Content = formCtx;
                var response = client.SendAsync(request, cancelToken).Result;
                var content = response.Content.ReadAsStringAsync().Result;
                var access = AssignType<OcelotAccessDto>(content);
                _ocelotToken = access.Token;
                _ocelotTokenExpiresTime = DateTime.Now.AddSeconds(access.ExpiresIn - 60);
            }

            baseClient.DefaultRequestHeaders.Add("Authorization", $"Bearer { OcelotToken }");
        }
        
        private T AssignType<T>(string content)
        {
            return typeof(T).IsAssignableFrom(typeof(string))
                ? (T)Convert.ChangeType(content, typeof(T))
                : JsonConvert.DeserializeObject<T>(content);
        }
    }
}
