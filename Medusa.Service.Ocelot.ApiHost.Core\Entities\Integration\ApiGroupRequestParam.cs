using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration
{
    /// <summary>
    /// ApiGroupRequestParams
    /// </summary>
    [EntityTable("ApiGroupRequestParams", "Api组合请求参数")]
    public class ApiGroupRequestParam
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? SchemaId { get; set; }

        /// <summary>
        /// 参数名称
        /// </summary>
        [EntityColumn(ColumnDescription = "参数名称", ColumnDataType = "varchar(50)")]
        public string SchemaName { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        [EntityColumn(ColumnDescription = "类型", ColumnDataType = "varchar(20)")]
        public string DataType { get; set; }

        /// <summary>
        /// 是否必须
        /// </summary>
        [EntityColumn(ColumnDescription = "是否必须")]
        public bool? Required { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [EntityColumn(ColumnDescription = "描述", ColumnDataType = "varchar(200)")]
        public string Description { get; set; }

        /// <summary>
        /// 默认值
        /// </summary>
        [EntityColumn(ColumnDescription = "默认值", ColumnDataType = "varchar(200)")]
        public string DefaultValue { get; set; }

        /// <summary>
        /// 上级Id
        /// </summary>
        [EntityColumn(ColumnDescription = "上级Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? UpperSchemaId { get; set; }

        /// <summary>
        /// Api组合主键Id
        /// </summary>
        [EntityColumn(ColumnDescription = "Api组合主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ApiGroupId { get; set; }
    }
}
