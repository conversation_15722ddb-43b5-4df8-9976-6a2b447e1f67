﻿using Medusa.Service.Ocelot.ApiHost.Application.Database;
using Medusa.Service.Ocelot.ApiHost.Application.OcelotApi;
using Medusa.Service.Ocelot.ApiHost.Core.ORM;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Medusa.Service.ThirdProvide.Application
{
    /// <summary>
    /// 服务注册
    /// </summary>
    public static class ServiceRegister
    {
        /// <summary>
        /// AddApplicationServices
        /// </summary>
        /// <param name="services">services</param>
        /// <param name="configuration">configuration</param>
        public static void AddApplicationServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<MyDbContext>();
            services.AddSingleton<IOcelotApiSerivce, OcelotApiSerivce>();
            services.AddSingleton<IDatabaseService, MySqlService>();
            services.AddSingleton<IDatabaseService, SqlService>();
        }
    }
}
