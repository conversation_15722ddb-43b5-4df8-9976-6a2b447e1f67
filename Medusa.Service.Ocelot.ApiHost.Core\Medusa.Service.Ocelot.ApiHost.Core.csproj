﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	<CodeAnalysisRuleSet>..\csharp.ruleset</CodeAnalysisRuleSet>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>bin\Debug\netcoreapp3.1\Medusa.Service.Ocelot.ApiHost.Core.xml</DocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DocumentationFile>bin\Debug\netcoreapp3.1\Medusa.Service.Ocelot.ApiHost.Core.xml</DocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MongoDB.Bson" Version="2.15.1" />
    <PackageReference Include="MongoDB.Driver" Version="2.15.1" />
    <PackageReference Include="Ocelot" Version="16.0.1" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="ORM\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\mt.enterprise.tools.shuiwu\src\MT.Enterprise.Core\MT.Enterprise.Core.csproj" />
    <ProjectReference Include="..\..\mt.enterprise.tools.shuiwu\src\MT.Enterprise.SDK\MT.Enterprise.SDK.csproj" />
    <ProjectReference Include="..\..\mt.enterprise.tools.shuiwu\src\MT.Enterprise.Utils\MT.Enterprise.Utils.csproj" />
  </ItemGroup>

</Project>
