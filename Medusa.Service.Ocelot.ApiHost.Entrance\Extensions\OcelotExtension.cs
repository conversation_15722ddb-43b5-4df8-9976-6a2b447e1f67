﻿using Medusa.Service.Ocelot.ApiHost.Application.Dtos;
using Medusa.Service.Ocelot.ApiHost.Application.Extensions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Ocelot.Configuration.File;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Formatting;
using System.Net.Http.Headers;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace Medusa.Service.Ocelot.ApiHost.Entrance.Extensions
{
    /// <summary>
    /// 
    /// </summary>
    public static class OcelotExtension
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="builder"></param>
        /// <param name="folder"></param>
        /// <param name="env"></param>
        /// <returns></returns>
        public static IConfigurationBuilder AddOcelotConfig(this IConfigurationBuilder builder, string folder, IWebHostEnvironment env)
        {
            string primaryConfigFile = MergeOcelotConfig(folder, env);
            builder.AddJsonFile(primaryConfigFile, false, true);

            return builder;
        }

        /// <summary>
        /// 删除上游配置的参数
        /// </summary>
        /// <param name="headers"></param>
        public static void RemoveOcelotHeaderKey(HttpRequestHeaders headers)
        {
            if (headers.Contains("Accept-Encoding"))
                headers.Remove("Accept-Encoding");

            if (headers.Contains("api-token"))
                headers.Remove("api-token");

            if (headers.Contains("request-module"))
                headers.Remove("request-module");

            if (headers.Contains("api-category"))
                headers.Remove("api-category");

            if (headers.Contains("receiving-mode"))
                headers.Remove("receiving-mode");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="responseDto"></param>
        /// <returns></returns>
        public static Task<HttpResponseMessage> ResponseTaskAsync(OcelotResponseDto responseDto)
        {
            HttpStatusCode statusCode = responseDto.ErrorCode == "0" ? HttpStatusCode.OK : responseDto.ErrorCode.ToEnum<HttpStatusCode>();

            var response = new HttpResponseMessage(statusCode)
            {
                Content = new ObjectContent(typeof(OcelotResponseDto), responseDto, new JsonMediaTypeFormatter())
            };

            var tsc = new TaskCompletionSource<HttpResponseMessage>();
            tsc.SetResult(response);

            return tsc.Task;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="responseDto"></param>
        /// <returns></returns>
        public static HttpResponseMessage OcelotResponse(OcelotResponseDto responseDto)
        {
            HttpStatusCode statusCode = responseDto.ErrorCode == "0" ? HttpStatusCode.OK : responseDto.ErrorCode.ToEnum<HttpStatusCode>();

            var response = new HttpResponseMessage(statusCode)
            {
                Content = new ObjectContent(typeof(OcelotResponseDto), responseDto, new JsonMediaTypeFormatter())
            };

            return response;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public static string GetOcelotConfigFolder()
        {
            var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            string config = string.Empty;
            if (!string.IsNullOrEmpty(environmentName) && (environmentName == "Local" || environmentName == "Development"))
            {
                if (environmentName == "Development")
                {
                    config = File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), "appsettings.Development.json"));
                }
                else if (environmentName == "Local")
                {
                    config = File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), "appsettings.Local.json"));
                }
            }
            else
            {
                config = File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json"));
            }

            return JObject.Parse(config)["OcelotConfigFolder"].ToString();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public static CancellationTokenSource GetCancellationTokenSource(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            cts.CancelAfter(TimeSpan.FromMinutes(20));
            return cts;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public static async Task<HttpRequestMessage> CloneHttpRequestMessageAsync(HttpRequestMessage req)
        {
            HttpRequestMessage clone = new HttpRequestMessage(req.Method, req.RequestUri); // Copy the request's content (via a MemoryStream) into the cloned object
            var ms = new MemoryStream();
            if (req.Content != null)
            {
                await req.Content.CopyToAsync(ms).ConfigureAwait(false); ms.Position = 0;
                clone.Content = new StreamContent(ms); // Copy the content headers
                if (req.Content.Headers != null)
                {
                    foreach (var h in req.Content.Headers)
                    {
                        clone.Content.Headers.Add(h.Key, h.Value);
                    }
                }
            }
            clone.Version = req.Version;

            foreach (KeyValuePair<string, object> prop in req.Properties)
            {
                clone.Properties.Add(prop);
            }
            foreach (var header in req.Headers)
            {
                clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            return clone;
        }

        private static string MergeOcelotConfig(string folder, IWebHostEnvironment env)
        {
            const string primaryConfigFile = "Ocelot/ocelot.json";

            const string globalConfigFile = "ocelot.global.json";

            const string subConfigPattern = @"^ocelot\.(.*?)\.json$";

            string excludeConfigName = env?.EnvironmentName != null ? $"ocelot.{env.EnvironmentName}.json" : string.Empty;

            var reg = new Regex(subConfigPattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);

            var files = new DirectoryInfo(folder)
                .EnumerateFiles()
                .Where(fi => reg.IsMatch(fi.Name) && (fi.Name != excludeConfigName))
                .ToList();

            var fileConfiguration = new FileConfiguration();

            foreach (var file in files)
            {
                if (files.Count > 1 && file.Name.Equals(primaryConfigFile, StringComparison.OrdinalIgnoreCase))
                {
                    continue;
                }

                var lines = File.ReadAllText(file.FullName);

                var config = JsonConvert.DeserializeObject<FileConfiguration>(lines);

                if (file.Name.Equals(globalConfigFile, StringComparison.OrdinalIgnoreCase))
                {
                    fileConfiguration.GlobalConfiguration = config.GlobalConfiguration;
                }

                fileConfiguration.Aggregates.AddRange(config.Aggregates);
                fileConfiguration.Routes.AddRange(config.Routes);
            }

            var json = JsonConvert.SerializeObject(fileConfiguration);

            File.WriteAllText(primaryConfigFile, json);
            return primaryConfigFile;
        }
    }
}

