﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Application.Dtos
{
    /// <summary>
    /// 
    /// </summary>
    public class OcelotPublishApiDto
    {
        /// <summary>
        /// 
        /// </summary>
        public OcelotPublishApiDto()
        {
            MockApis = new List<Guid>();
            HttpApis = new List<Guid>();
        }

        /// <summary>
        /// 
        /// </summary>
        public List<Guid> MockApis { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<Guid> HttpApis { get; set; }
    }
}
