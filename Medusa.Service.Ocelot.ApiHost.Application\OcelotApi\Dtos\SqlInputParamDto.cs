﻿using Newtonsoft.Json.Linq;

namespace Medusa.Service.Ocelot.ApiHost.Application.OcelotApi.Dtos
{
    /// <summary>
    /// 
    /// </summary>
    public class SqlInputParamDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string ParamName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public JToken ParamValue { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool IsArray { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ArrayParamName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public JToken ArrayParamValue { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ArrayParamName_Field { get; set; }
    }
}
