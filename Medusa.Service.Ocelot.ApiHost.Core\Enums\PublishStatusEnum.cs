﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Core.Enums
{
    /// <summary>
    /// 发布状态
    /// </summary>
    public enum PublishStatusEnum
    {
        /// <summary>
        /// 已发布
        /// </summary>
        [Description("已发布")]
        Published = 1,

        /// <summary>
        /// 已下线
        /// </summary>
        [Description("已下线")]
        Offline = 0,

        /// <summary>
        /// 未发布
        /// </summary>
        [Description("未发布")]
        Unpublished = -1
    }
}
