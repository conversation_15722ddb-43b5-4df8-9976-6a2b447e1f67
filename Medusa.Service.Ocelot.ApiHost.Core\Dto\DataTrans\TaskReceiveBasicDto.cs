﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Core.Dtos
{
    /// <summary>
    /// 任务 - 数据接收基础信息
    /// </summary>
    public class TaskReceiveBasicDto
    {
        /// <summary>
        /// 任务号
        /// </summary>
        public string TaskNumber { get; set; }

        /// <summary>
        /// 子任务号
        /// </summary>
        public string SubTaskNumber { get; set; }

        /// <summary>
        /// 模型Id
        /// </summary>
        public Guid? ModelId { get; set; }

        /// <summary>
        /// 模型编码
        /// </summary>
        public string ModelCode { get; set; }

        /// <summary>
        /// 模型名称
        /// </summary>
        public string ModelName { get; set; }

        /// <summary>
        /// 接收器
        /// </summary>
        public string Receiver { get; set; }

        /// <summary>
        /// 接收器Id
        /// </summary>
        public Guid? ReceiveId { get; set; }
    }
}
