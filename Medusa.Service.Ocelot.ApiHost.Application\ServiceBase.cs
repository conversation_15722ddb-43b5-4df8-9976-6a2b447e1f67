﻿using System.Data;
using Newtonsoft.Json.Linq;

namespace Medusa.Service.Ocelot.ApiHost.Application
{
    /// <summary>
    /// ServiceBase
    /// </summary>
    public class ServiceBase : IServiceBase
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public DbType MappingDbType(JTokenType type)
        {
            DbType dbType = DbType.AnsiString;
            switch (type)
            {
                case JTokenType.None:
                    break;
                case JTokenType.Object:
                    break;
                case JTokenType.Array:
                    break;
                case JTokenType.Constructor:
                    break;
                case JTokenType.Property:
                    break;
                case JTokenType.Comment:
                    break;
                case JTokenType.Integer:
                    dbType = DbType.Int32;
                    break;
                case JTokenType.Float:
                    dbType = DbType.Decimal;
                    break;
                case JTokenType.String:
                    dbType = DbType.String;
                    break;
                case JTokenType.Boolean:
                    dbType = DbType.Boolean;
                    break;
                case JTokenType.Null:
                    break;
                case JTokenType.Undefined:
                    break;
                case JTokenType.Date:
                    dbType = DbType.DateTime;
                    break;
                case JTokenType.Raw:
                    break;
                case JTokenType.Bytes:
                    break;
                case JTokenType.Guid:
                    dbType = DbType.String;
                    break;
                case JTokenType.Uri:
                    break;
                case JTokenType.TimeSpan:
                    break;
                default:
                    break;
            }

            return dbType;
        }
    }
}
