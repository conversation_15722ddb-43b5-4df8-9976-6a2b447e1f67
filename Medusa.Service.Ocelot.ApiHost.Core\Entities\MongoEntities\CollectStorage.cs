﻿using System;
using Medusa.Service.Ocelot.ApiHost.Core.Dtos;
using MongoDB.Bson.Serialization.Attributes;

namespace Medusa.Service.Ocelot.ApiHost.Core.Entities.MongoEntities
{
    /// <summary>
    /// 收集仓库
    /// </summary>
    [BsonIgnoreExtraElements]
    public class CollectStorage : TaskReceiveBasicDto
    {
        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime AddDate { get; set; }

        /// <summary>
        /// 是否分页
        /// </summary>
        public bool? IsPagination { get; set; }

        /// <summary>
        /// 页码（分页生效）
        /// </summary>
        public int? PageIndex { get; set; }

        /// <summary>
        /// 任务参数缓存
        /// </summary>
        public string TaskParams { get; set; }
    }
}
