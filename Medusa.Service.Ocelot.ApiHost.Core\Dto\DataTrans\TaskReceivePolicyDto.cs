﻿using System.Collections.Generic;

namespace Medusa.Service.Ocelot.ApiHost.Core.Dtos
{
    /// <summary>
    /// 任务-数据接收策略
    /// </summary>
    public class TaskReceivePolicyDto
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public TaskReceivePolicyDto()
        {
            Attributes = new List<TaskReceiveAttributeDto>();
        }

        /// <summary>
        /// 基础信息
        /// </summary>
        public TaskReceiveBasicDto Basic { get; set; }

        /// <summary>
        /// 入参参数
        /// </summary>
        public dynamic InputParamBody { get; set; }

        /// <summary>
        /// 接收模式
        /// </summary>
        public string ReceivingMode { get; set; }

        /// <summary>
        /// 分页策略
        /// </summary>
        public TaskReceivePaginationDto Pagination { get; set; }

        /// <summary>
        /// 参数特性
        /// </summary>
        public List<TaskReceiveAttributeDto> Attributes { get; set; }

        /// <summary>
        /// 数据处理策略
        /// </summary>
        public TaskReceiveDataProcessDto DataProcess { get; set; }
    }
}
