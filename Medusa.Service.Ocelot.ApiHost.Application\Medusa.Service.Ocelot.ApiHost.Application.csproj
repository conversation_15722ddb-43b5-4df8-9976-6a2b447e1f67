﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <CodeAnalysisRuleSet>..\csharp.ruleset</CodeAnalysisRuleSet>
	  <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
    <DocumentationFile>bin\Debug\netcoreapp3.1\Medusa.Service.Ocelot.ApiHost.Application.xml</DocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DocumentationFile>bin\Debug\netcoreapp3.1\Medusa.Service.Ocelot.ApiHost.Application.xml</DocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DynamicExpresso.Core" Version="2.13.0" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Medusa.Service.Ocelot.ApiHost.Core\Medusa.Service.Ocelot.ApiHost.Core.csproj" />
  </ItemGroup>

</Project>
