﻿using System.ComponentModel;

namespace Medusa.Service.Ocelot.ApiHost.Core.Enums
{
    /// <summary>
    /// BackendServiceType
    /// </summary>
    public enum BackendServiceType
    {
        /// <summary>
        /// Http(s)
        /// </summary>
        [Description("Http(s)")]
        Https,

        /// <summary>
        /// Web Service
        /// </summary>
        [Description("Web Service")]
        WebService,

        /// <summary>
        /// Sql Server
        /// </summary>
        [Description("Database")]
        Database,

        /// <summary>
        /// FlowAPI
        /// </summary>
        [Description("FlowAPI")]
        FlowAPI,

        /// <summary>
        /// Mock
        /// </summary>
        [Description("Mock")]
        Mock
    }
}
