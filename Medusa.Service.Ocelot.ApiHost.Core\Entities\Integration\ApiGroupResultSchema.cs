using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration
{
    /// <summary>
    /// ApiGroupResultSchema
    /// </summary>
    [EntityTable("ApiGroupResultSchema", "Api组合请求结果结构")]
    public class ApiGroupResultSchema
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? SchemaId { get; set; }

        /// <summary>
        /// 字段名称
        /// </summary>
        [EntityColumn(ColumnDescription = "字段名称", ColumnDataType = "varchar(50)")]
        public string SchemaName { get; set; }

        /// <summary>
        /// 字段类型
        /// </summary>
        [EntityColumn(ColumnDescription = "字段类型", ColumnDataType = "varchar(20)")]
        public string DataType { get; set; }

        /// <summary>
        /// 字段描述
        /// </summary>
        [EntityColumn(ColumnDescription = "字段描述", ColumnDataType = "varchar(255)")]
        public string Description { get; set; }

        /// <summary>
        /// 上级Id
        /// </summary>
        [EntityColumn(ColumnDescription = "上级Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? UpperSchemaId { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [EntityColumn(ColumnDescription = "排序")]
        public int? OrderIndex { get; set; }

        /// <summary>
        /// Api组合主键Id
        /// </summary>
        [EntityColumn(ColumnDescription = "Api组合主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ApiGroupId { get; set; }
    }
}
