﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Application.Dtos
{
    /// <summary>
    /// 
    /// </summary>
    public class ExecSqlDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string Sql { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DbHost { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DbName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Password { get; set; }
    }
}
