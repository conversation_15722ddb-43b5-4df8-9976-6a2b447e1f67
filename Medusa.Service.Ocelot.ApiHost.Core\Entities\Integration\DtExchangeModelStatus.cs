﻿using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration
{
    /// <summary>
    /// 模型运行状态
    /// </summary>
    [EntityTable("DT_ExchangeModelStatus", "模型运行状态")]
    public class DtExchangeModelStatus
    {
        /// <summary>
        /// 交换模型Id
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "交换模型Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ExchangeModelId { get; set; }

        /// <summary>
        /// 当前任务号
        /// </summary>
        [EntityColumn(ColumnDescription = "当前任务号")]
        public string CurrentTaskNumber { get; set; }

        /// <summary>
        /// 当前子任务号
        /// </summary>
        [EntityColumn(ColumnDescription = "当前子任务号")]
        public string CurrentSubTaskNumber { get; set; }

        /// <summary>
        /// 当前任务阶段
        /// </summary>
        [EntityColumn(ColumnDescription = "当前任务阶段")]
        public string CurrentTaskStage { get; set; }

        /// <summary>
        /// 当前任务状态
        /// </summary>
        [EntityColumn(ColumnDescription = "当前任务状态")]
        public int? CurrentTaskStatus { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [EntityColumn(ColumnDescription = "错误信息")]
        public string ErrMsg { get; set; }
    }
}
