﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Core.Enums
{
    /// <summary>
    /// 任务状态
    /// </summary>
    public enum TaskStatus
    {
        /// <summary>
        /// 失败
        /// </summary>
        [Description("失败")]
        Fail = -1,

        /// <summary>
        /// 成功
        /// </summary>
        [Description("成功")]
        Success = 1
    }
}
