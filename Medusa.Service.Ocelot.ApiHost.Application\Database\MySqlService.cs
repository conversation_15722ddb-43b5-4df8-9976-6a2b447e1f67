﻿using System;
using System.Collections.Generic;
using System.Data;
using Medusa.Service.Ocelot.ApiHost.Application;
using Medusa.Service.Ocelot.ApiHost.Application.Dtos;
using Medusa.Service.Ocelot.ApiHost.Application.Enum;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration;
using Medusa.Service.Ocelot.ApiHost.Core.ORM;
using Microsoft.Extensions.DependencyInjection;
using SqlSugar;

namespace Medusa.Service.Ocelot.ApiHost.Application.Database
{
    /// <summary>
    /// My SQL
    /// </summary>
    public class MySqlService : ServiceBase, IDatabaseService
    {
        private readonly MyDbContext _dbContext;

        /// <summary>
        /// Type：MySql
        /// </summary>
        public DatabaseType Type => DatabaseType.MySql;

        /// <summary>
        /// My SQL
        /// </summary>
        /// <param name="serviceProvider">服务依赖</param>
        public MySqlService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
        }

        /// <summary>
        /// 执行Sql
        /// </summary>
        /// <param name="dbConf">dbConf</param>
        /// <param name="sql">sql</param>
        /// <param name="parameters"></param>
        /// <param name="outTable"></param>
        public void ExceSql(ServicesDatabase dbConf, string sql, List<SugarParameter> parameters, out DataTable outTable)
        {
            string connection = $"Server={dbConf.DbHost};Database={dbConf.DbName};Uid={dbConf.UserName};Pwd={dbConf.Password};";
            var client = _dbContext.MySqlClient(connection);

            try
            {
                client.Ado.BeginTran();

                if (sql.Trim().ToLower().StartsWith("select "))
                {
                    outTable = client.Ado.GetDataTable(sql, parameters);
                }
                else
                {
                    client.Ado.ExecuteCommand(sql, parameters);
                    outTable = null;
                }

                client.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                client.Ado.RollbackTran();
                outTable = null;
                throw ex;
            }
        }
    }
}
