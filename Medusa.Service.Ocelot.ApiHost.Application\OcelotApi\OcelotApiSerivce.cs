﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Medusa.Service.Ocelot.ApiHost.Application.Database;
using Medusa.Service.Ocelot.ApiHost.Application.Dtos;
using Medusa.Service.Ocelot.ApiHost.Application.Enum;
using Medusa.Service.Ocelot.ApiHost.Application.Extensions;
using Medusa.Service.Ocelot.ApiHost.Application.OcelotApi.Dtos;
using Medusa.Service.Ocelot.ApiHost.Core.Constants;
using Medusa.Service.Ocelot.ApiHost.Core.Dtos;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration;
using Medusa.Service.Ocelot.ApiHost.Core.Enums;
using Medusa.Service.Ocelot.ApiHost.Core.Extensions;
using Medusa.Service.Ocelot.ApiHost.Core.ORM;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Utils;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;

namespace Medusa.Service.Ocelot.ApiHost.Application.OcelotApi
{
    /// <summary>
    /// Ocelot Api
    /// </summary>
    public class OcelotApiSerivce : ServiceBase, IOcelotApiSerivce
    {
        private readonly MyDbContext _dbContext;
        private readonly JToken ids4Mapping;

        readonly IEnumerable<IDatabaseService> _databaseServices;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="serviceProvider">服务依赖</param>
        public OcelotApiSerivce(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            _databaseServices = serviceProvider.GetService<IEnumerable<IDatabaseService>>();
            var appSettings = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");
            ids4Mapping = appSettings?["Ids4Mapping"];
        }

        #region 被授权的API查询

        /// <summary>
        /// 获取三方接口
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>返回接口中信息</returns>
        public List<InterfaceInfoDto> GetThirdInterfaces(InterfaceInfoQueryDto queryDto)
        {
            var app = _dbContext.Integration.Queryable<Applications>()
                .Where(x => x.AppCode == queryDto.AppCode && x.Password == queryDto.Password)
                .First();
            if (app == null)
            {
                throw new Exception("无效的应用授权");
            }

            var interfaceInfos = _dbContext.Integration.Queryable<ApplicationApis, Apis, ApiBase, ApiCateogry>((application, api, apiBase, category) => new object[]
                  {
                    JoinType.Inner, application.ApiId == api.ApiId,
                    JoinType.Inner, api.CurrentVersionId == apiBase.ApiVersionId && apiBase.ApiId == api.ApiId,
                    JoinType.Left, apiBase.CategoryId == category.CategoryId
                  })
                 .Where(application => application.ApplicationId == app.ApplicationId)
                 .Select((application, api, apiBase, category) => new InterfaceInfoDto()
                 {
                     ApiId = application.ApiId.Value,
                     ApiName = apiBase.ApiName,
                     RequestMethod = apiBase.RequestMethod,
                     RequestPath = apiBase.RequestPath,
                     Description = apiBase.Description,
                     IsSecurityCert = apiBase.IsSecurityCert.Value,
                     CategoryId = category.CategoryId,
                     CategoryCode = category.CategoryCode,
                     CategoryName = category.CategoryName
                 }).ToList();

            return interfaceInfos;
        }

        /// <summary>
        /// 获取组合接口
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>返回接口中信息</returns>
        public List<InterfaceInfoDto> GetThirdGroupInterfaces(InterfaceInfoQueryDto queryDto)
        {
            var app = _dbContext.Integration.Queryable<Applications>()
                .Where(x => x.AppCode == queryDto.AppCode && x.Password == queryDto.Password)
                .First();
            if (app == null)
            {
                throw new Exception("无效的应用授权");
            }

            var interfaceInfos = _dbContext.Integration.Queryable<ApplicationApis, ApiGroup>((t1, t2) => new object[]
                 {
                    JoinType.Inner, t1.ApiGroupId == t2.ApiGroupId,
                 })
                 .Where((t1, t2) => t1.ApplicationId == app.ApplicationId)
                 .Select((t1, t2) => new InterfaceInfoDto()
                 {
                     ApiId = t1.ApiGroupId.Value,
                     ApiName = t2.ApiGroupName,
                     RequestMethod = t2.RequestMethod,
                     RequestPath = t2.RequestPath,
                     Description = t2.Description
                 }).ToList();

            return interfaceInfos;
        }

        /// <summary>
        /// 获取三方接口结构
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>接口结构</returns>
        public dynamic GetThirdInterfaceSchema(InterfaceSchemaQueryDto queryDto)
        {
            var appApi = _dbContext.Integration.Queryable<Applications, ApplicationApis, ApiPublishInfo, Apis>((application, appApi, apiPub, api) => new object[]
                {
                    JoinType.Inner, application.ApplicationId == appApi.ApplicationId && application.AppCode == queryDto.AppCode && application.Password == queryDto.Password,
                    JoinType.Inner, apiPub.ApiId == appApi.ApiId,
                    JoinType.Inner, api.CurrentVersionId == apiPub.ApiVersionId,
                }).Where((application, appApi, apiPub, api) => appApi.ApiId == queryDto.ApiId && api.ApiId == queryDto.ApiId)
                  .Select((application, appApi, apiPub, api) => apiPub).First();

            if (appApi == null)
            {
                throw new Exception("无效的应用授权");
            }

            return JsonConvert.DeserializeObject<dynamic>(appApi.ApiSchemaJson);
        }

        /// <summary>
        /// 获取组合接口结构
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>组合接口结构</returns>
        public dynamic GetThirdGroupInterfacesSchema(InterfaceSchemaQueryDto queryDto)
        {
            var appApi = _dbContext.Integration.Queryable<Applications, ApplicationApis, ApiGroup, ApiGroupPublishInfo>((t1, t2, t3, t4) => new object[]
            {
                    JoinType.Inner, t1.ApplicationId == t2.ApplicationId && t1.AppCode == queryDto.AppCode && t1.Password == queryDto.Password,
                    JoinType.Inner, t2.ApiGroupId == t3.ApiGroupId,
                    JoinType.Inner, t3.CurrentVersionNo == t4.VersionNo
            })
                .Where((t1, t2, t3, t4) => t3.ApiGroupId == queryDto.ApiId)
                .Select((t1, t2, t3, t4) => t4).First();

            if (appApi == null)
            {
                throw new Exception("无效的应用授权");
            }

            return JsonConvert.DeserializeObject<dynamic>(appApi.ApiGroupSchemaJson);
        }

        #endregion

        #region Http/Https - 异步

        /// <summary>
        /// Http/Https 请求信息处理
        /// </summary>
        /// <param name="api">Api对象</param>
        /// <param name="requestMsg">请求信息</param>
        /// <param name="processAction">处理动作</param>
        /// <returns></returns>
        public Task<HttpRequestMessage> HttpRequestMsgProcessAsync(Apis api, HttpRequestMessage requestMsg, Dictionary<string, string> processAction)
        {
            (Dictionary<string, object>, ServiceApis) downstreamMapping = (null, null);

            // 入参映射
            if (processAction.ContainsKey("RequestModule") && processAction["RequestModule"] == ApiRequestModule.Mapping.ToString())
            {
                var requestContent = requestMsg.Content.ReadAsStringAsync().Result;
                var postJToken = JsonConvert.DeserializeObject<JToken>(requestContent);
                downstreamMapping = InputParameterMapping(requestMsg, api, postJToken, BackendServiceType.Https);
            }

            // 下游API认证
            if (processAction.ContainsKey("DownstreamAuth") && processAction["DownstreamAuth"].ToOurBoolean())
            {
                if (downstreamMapping.Item1 == null)
                {
                    DownstreamApiAuth(api, null, requestMsg);
                }
                else
                {
                    DownstreamApiAuth(api, downstreamMapping.Item2, requestMsg);
                }
            }

            return new Task<HttpRequestMessage>(() => requestMsg);
        }

        #endregion

        #region Mock方式

        /// <summary>
        /// 获取Mock信息
        /// </summary>
        /// <param name="api">api对象</param>
        /// <returns>返回Mock信息</returns>
        public OcelotResponseDto GetInterfaceMockInfo(Apis api)
        {
            var mockValue = _dbContext.Integration.Queryable<ApiBase, ApisServiceMapping, ApisServiceMappingMock>((apiBase, serviceMapping, serviceMappingMock) => new object[]
                {
                    JoinType.Inner, apiBase.ApiBaseId == serviceMapping.ApiBaseId,
                    JoinType.Inner, serviceMapping.ServiceMappingId == serviceMappingMock.ServiceMappingId,
                }).Where(apiBase => apiBase.ApiId == api.ApiId && apiBase.ApiVersionId == api.CurrentVersionId)
                  .Select((apiBase, serviceMapping, serviceMappingMock) => serviceMappingMock.MockValue)
                  .First();

            if (string.IsNullOrEmpty(mockValue))
            {
                return ResponseDefault(mockValue);
            }

            return ResponseMapping(mockValue, api);
        }

        #endregion

        #region 数据库方式

        /// <summary>
        /// sql exce
        /// </summary>
        /// <param name="api">api对象</param>
        /// <param name="postJsonToken">参数</param>
        /// <returns>Database信息</returns>
        public OcelotResponseDto ExecSqlForDb(Apis api, JToken postJsonToken)
        {
            var dbInfo = _dbContext.Integration.Queryable<ApiBase, ApisServiceMapping, ApisServiceMappingDatabase, ServicesDatabase>((ab, asm, asmdb, sdb) => new object[]
                {
                    JoinType.Inner, ab.ApiBaseId == asm.ApiBaseId,
                    JoinType.Inner, asm.ServiceMappingId == asmdb.ServiceMappingId,
                    JoinType.Inner, asmdb.ServiceId == sdb.ServiceId,
                }).Where(ab => ab.ApiId == api.ApiId && ab.ApiVersionId == api.CurrentVersionId)
                  .Select((ab, asm, asmdb, sdb) => new ExecSqlDto
                  {
                      Sql = asmdb.QuerySql,
                      DataType = asmdb.DbType,
                      DbHost = sdb.DbHost,
                      DbName = sdb.DbName,
                      UserName = sdb.UserName,
                      Password = sdb.Password
                  }).First();

            if (dbInfo == null)
            {
                return new OcelotResponseDto() { ErrorCode = "500", ErrorMessage = "找不到下游Database信息" };
            }

            var db = _databaseServices.First(r => r.Type == dbInfo.DataType.ToOurEnum<DatabaseType>());
            if (db == null)
            {
                return new OcelotResponseDto() { ErrorCode = "500", ErrorMessage = $"找不到{dbInfo.DataType}的实例" };
            }

            var dbConf = new ServicesDatabase()
            {
                DbHost = dbInfo.DbHost,
                DbName = dbInfo.DbName,
                UserName = dbInfo.UserName,
                Password = dbInfo.Password
            };

            List<SugarParameter> parameters = new List<SugarParameter>();
            List<SqlInputParamDto> inputParamValue = new List<SqlInputParamDto>();
            StringBuilder sqlBuilder = new StringBuilder();

            var sqlAllParams = ExtractSqlParams(dbInfo.Sql);

            // 获取参数值
            sqlAllParams.ForEach(r =>
            {
                SqlInputParamDto inputParam = new SqlInputParamDto();
                var paramName = r.TrimStart('{').TrimEnd('}');

                inputParam.ParamName = r;

                // 判断是否循环，Array参数被引用代表是循环语句
                if (paramName.Split('|').Last().Split('.').First() == "Arr")
                {
                    var lastParam = paramName.Split('|').Last();
                    paramName = string.Join("|", paramName.Split('|').ToList().Take(paramName.Split('|').Length - 1));
                    inputParam.ArrayParamName = "{" + paramName + "}";
                    inputParam.IsArray = true;

                    var schemaPaths = paramName.Split('|').ToList().Select(x => x.Split('.')[1]);
                    inputParam.ArrayParamValue = schemaPaths.Aggregate(postJsonToken, (current, schemaName) => current[schemaName]);
                    inputParam.ArrayParamName_Field = lastParam.Split('.')[1];
                }
                else
                {
                    var schemaPaths = paramName.Split('|').Select(x => x.Split('.')[1]).ToList();
                    inputParam.ParamValue = schemaPaths.Aggregate(postJsonToken, (current, schemaName) => current[schemaName]);
                }

                inputParamValue.Add(inputParam);
            });

            var sqls = dbInfo.Sql.Split(';').ToList();
            sqls.ForEach(r =>
            {
                var sql = r;
                var sqlParams = ExtractSqlParams(r);

                // 判断是否循环，Array参数被引用代表是循环语句
                if (sqlParams.Any(x => inputParamValue.Any(pv => pv.ParamName == x && pv.IsArray)))
                {
                    var currentSqlInputParams = inputParamValue.Where(x => sqlParams.Any(p => p == x.ParamName)).ToList();
                    var arrParams = currentSqlInputParams.Where(x => x.IsArray).Select(x => x.ArrayParamName).Distinct().ToList();

                    if (arrParams.Count == 1)
                    {
                        var arrParamValue = currentSqlInputParams.Find(x => x.ArrayParamName == arrParams[0]).ArrayParamValue;
                        if (arrParamValue != null && arrParamValue.Count() > 0)
                        {
                            for (int i = 0; i < arrParamValue.Count(); i++)
                            {
                                sql = r;
                                sqlParams.ForEach(x =>
                                {
                                    var inputParam = currentSqlInputParams.Find(p => p.ParamName == x);
                                    var paramValue = inputParam.IsArray ? arrParamValue[i][inputParam.ArrayParamName_Field] : inputParam.ParamValue;
                                    var sugarParameter = new SugarParameter($"@P_{Guid.NewGuid().ToString().Replace("-", string.Empty)}", paramValue);
                                    sql = sql.Replace(x, sugarParameter.ParameterName);
                                    parameters.Add(sugarParameter);
                                });

                                sqlBuilder.AppendLine(sql);
                            }
                        }
                    }

                    if (arrParams.Count > 1)
                    {
                        // TODO: 多个数组参数进行交叉处理
                    }
                }
                else
                {
                    var currentSqlInputParams = inputParamValue.Where(x => sqlParams.Any(p => p == x.ParamName)).ToList();
                    sqlParams.ForEach(x =>
                    {
                        var inputParam = currentSqlInputParams.Find(p => p.ParamName == x);
                        var sugarParameter = new SugarParameter($"@P_{Guid.NewGuid().ToString().Replace("-", string.Empty)}", inputParam.ParamValue, MappingDbType(inputParam.ParamValue.Type));
                        sql = sql.Replace(x, sugarParameter.ParameterName);
                        parameters.Add(sugarParameter);
                    });

                    sqlBuilder.AppendLine(sql);
                }
            });

            db.ExceSql(dbConf, sqlBuilder.ToString(), parameters, out DataTable outTable);

            if (outTable != null)
            {
                return ResponseMapping(JsonConvert.SerializeObject(outTable), api);
            }
            else
            {
                return new OcelotResponseDto() { ErrorCode = "0" };
            }
        }

        #endregion

        #region 数据交换

        /// <summary>
        /// 更新模型运行状态
        /// </summary>
        /// <param name="statusEntity"></param>
        public void UpdateExchangeModelStatus(DtExchangeModelStatus statusEntity)
        {
            if (!statusEntity.ExchangeModelId.HasValue)
            {
                throw new Exception("ExchangeModelId Missing");
            }

            var exists = _dbContext.Integration.Queryable<DtExchangeModelStatus>().Where(r => r.ExchangeModelId == statusEntity.ExchangeModelId).Any();
            if (exists)
            {
                _dbContext.Integration.Updateable(statusEntity).ExecuteCommand();
            }
            else
            {
                _dbContext.Integration.Insertable(statusEntity).ExecuteCommand();
            }
        }

        /// <summary>
        /// 添加日志
        /// </summary>
        /// <param name="receiveBasic">接收基础数据</param>
        /// <param name="taskStage"></param>
        /// <param name="taskStatus">1成功，-1失败</param>
        /// <param name="dataMsg"></param>
        /// <param name="executionTime"></param>
        /// <param name="errMsg"></param>
        public void AddLog(TaskReceiveBasicDto receiveBasic, TaskStage taskStage, int taskStatus, string dataMsg, DateTime? executionTime, string errMsg = "")
        {
            var log = new LogDataExchange()
            {
                TaskNumber = receiveBasic.TaskNumber,
                SubTaskNumber = receiveBasic.SubTaskNumber,
                TaskStage = taskStage.ToString(),
                TaskStatus = taskStatus,
                ExecutionTime = executionTime.HasValue ? executionTime : DateTime.Now,
                ModelCode = receiveBasic.ModelCode,
                ModeName = receiveBasic.ModelName,
                ExchangeModelId = receiveBasic.ModelId,
                ReceiveId = receiveBasic.ReceiveId,
                Receiver = receiveBasic.Receiver,
                DataMsg = dataMsg,
                ErrMsg = errMsg
            };

            _dbContext.Integration.Insertable(log).ExecuteCommand();
        }

        #endregion

        #region 各接口公用方法

        /// <summary>
        /// 获取API
        /// </summary>
        /// <param name="apiToken">apiToken</param>
        /// <returns>API</returns>
        public Apis GetApis(string apiToken)
        {
            var apiId = new Guid(Crypto.Base64Decrypt(Crypto.AESDecrypt(apiToken, AESConstant.AesKey, AESConstant.AseVectorKey)));

            var result = _dbContext.Integration.Queryable<Apis>()
                .Where(r => r.ApiId == apiId).ToList();

            if (result != null && result.Count > 0)
            {
                return result[0];
            }

            throw new Exception("集成中心没有配置此API");
        }

        /// <summary>
        /// 入参映射
        /// </summary>
        /// <param name="request"></param>
        /// <param name="api"></param>
        /// <param name="postValue"></param>
        /// <param name="serviceType"></param>
        public (Dictionary<string, object>, ServiceApis) InputParameterMapping(HttpRequestMessage request, Apis api, JToken postValue, BackendServiceType serviceType)
        {
            var item = GetDownstreamParamsMapping(api, postValue);
            var dictParamsMappings = item.Item1;
            var downstreamApi = item.Item2;

            // query
            if (dictParamsMappings.ContainsKey("Query"))
            {
                Dictionary<string, object> dictQueryMapping = dictParamsMappings["Query"] as Dictionary<string, object>;
                Dictionary<string, object> dictQuery = new Dictionary<string, object>();
                dictQueryMapping.ToList().ForEach(x =>
                {
                    // 过滤NULL值
                    if (x.Value != null)
                    {
                        dictQuery[x.Key] = x.Value;
                    }
                });
                string queryString = string.Join("&", dictQuery.ToList().Select(x => $"{x.Key}={x.Value}"));

                request.RequestUri = new Uri(request.RequestUri.ToString() + "?" + queryString);
            }

            // header
            if (dictParamsMappings.ContainsKey("Header"))
            {
                Dictionary<string, object> dictHeaderMapping = dictParamsMappings["Header"] as Dictionary<string, object>;
                dictHeaderMapping.ToList().ForEach(x =>
                {
                    // 过滤NULL值
                    if (x.Value != null)
                    {
                        request.Headers.Add(x.Key, x.Value?.ToOurString());
                    }
                });
            }

            // path
            if (dictParamsMappings.ContainsKey("Path"))
            {
                Dictionary<string, object> dictPathMapping = dictParamsMappings["Path"] as Dictionary<string, object>;
                string uri = request.RequestUri.ToString();
                dictPathMapping.ToList().ForEach(x =>
                {
                    // 过滤NULL值
                    if (x.Value != null)
                    {
                        uri = uri.Replace("{" + x.Key + "}", x.Value.ToOurString());
                    }
                });

                request.RequestUri = new Uri(uri);
            }

            // body
            if (dictParamsMappings.ContainsKey("Body"))
            {
                var dictBody = dictParamsMappings["Body"];
                switch (serviceType)
                {
                    case BackendServiceType.WebService:
                        var wsContent = JsonConvert.DeserializeObject<Dictionary<string, string>>(JsonConvert.SerializeObject(dictBody));
                        HttpContent httpContentWs = new FormUrlEncodedContent(wsContent);
                        request.Content = httpContentWs;
                        break;
                    default:
                        if (string.IsNullOrEmpty(downstreamApi?.DataFormat) || downstreamApi?.DataFormat == "json")
                        {
                            var bodyJson = JsonConvert.SerializeObject(dictBody);
                            HttpContent httpContent = new StringContent(bodyJson);
                            httpContent.Headers.ContentType = new MediaTypeHeaderValue(GetPostContentType(downstreamApi?.DataFormat));
                            request.Content = httpContent;
                        }

                        if (downstreamApi?.DataFormat == "x-www-form-urlencoded")
                        {
                            var bodyJson = JsonConvert.SerializeObject(dictBody);
                            var formParams = JsonConvert.DeserializeObject<Dictionary<string, string>>(bodyJson);
                            FormUrlEncodedContent httpContent = new FormUrlEncodedContent(formParams);
                            httpContent.Headers.ContentType = new MediaTypeHeaderValue(GetPostContentType(downstreamApi?.DataFormat));
                            request.Content = httpContent;
                        }

                        break;
                }
            }

            return item;
        }

        /// <summary>
        /// 结果映射
        /// </summary>
        /// <param name="responseValue">数据</param>
        /// <param name="api">api对象</param>
        /// <returns>映射后的结果</returns>
        public OcelotResponseDto ResponseMapping(string responseValue, Apis api)
        {
            var jToken = JsonConvert.DeserializeObject<JToken>(responseValue);

            // 返回结果结构
            var schemas = _dbContext.Integration.Queryable<ApiBase, ApiResultSchema>((apiBase, apiRes) => new object[]
                {
                    JoinType.Inner, apiBase.ApiBaseId == apiRes.ApiBaseId,
                }).Where(apiBase => apiBase.ApiId == api.ApiId && apiBase.ApiVersionId == api.CurrentVersionId)
                  .Select((apiBase, apiRes) => apiRes).ToList();

            // 映射结构
            var mappings = _dbContext.Integration.Queryable<ApiBase, ApiResultDesignMappingDto>((apiBase, apiRes) => new object[]
              {
                    JoinType.Inner, apiBase.ApiBaseId == apiRes.ApiBaseId,
              }).Where(apiBase => apiBase.ApiId == api.ApiId && apiBase.ApiVersionId == api.CurrentVersionId)
              .Select((apiBase, apiRes) => apiRes).ToList();
            mappings.ForEach(r =>
            {
                if (r.DataSource == MappingDataSource.ResultSchema.ToOurString() && !string.IsNullOrEmpty(r.DataSourceValue))
                {
                    r.MappingSchemaPath = new List<string>();
                    var schema = schemas.Find(x => x.SchemaId == r.DataSourceValue.ToOurGuid());
                    while (schema != null)
                    {
                        if (schema.SchemaName != "root")
                        {
                            r.MappingSchemaPath.Add(schema.SchemaName);
                        }

                        schema = schemas.Find(x => x.SchemaId == schema.UpperSchemaId);
                    }

                    r.MappingSchemaPath.Reverse();
                }
            });

            var isMapper = ResultIsMapper(mappings);
            var responseSchemas = GetApiResponseSchemas(schemas, mappings);
            var jObject = responseSchemas.ToMappingResponse(jToken, isMapper);

            return ResponseDefault(jObject);
        }

        /// <summary>
        /// 返回默认结果数据
        /// </summary>
        /// <param name="responseValue">responseValue</param>
        /// <returns>结果数据</returns>
        public OcelotResponseDto ResponseDefault(object responseValue)
        {
            return new OcelotResponseDto() { ErrorCode = "0", Data = responseValue };
        }

        /// <summary>
        /// 获取Api组合
        /// </summary>
        /// <param name="apiToken">apiToken</param>
        /// <returns></returns>
        public ApiGroupDto GetApiGroup(string apiToken)
        {
            var apiGroupId = new Guid(Crypto.Base64Decrypt(Crypto.AESDecrypt(apiToken, AESConstant.AesKey, AESConstant.AseVectorKey)));

            var apiGroup = _dbContext.Integration.Queryable<ApiGroupDto>().InSingle(apiGroupId);

            if (apiGroup == null)
            {
                return null;
            }

            apiGroup.RequestParams = _dbContext.Integration.Queryable<ApiGroupRequestParam>()
                .Where(t1 => t1.ApiGroupId == apiGroupId)
                .ToList();

            var groupItems = _dbContext.Integration.Queryable<ApiGroupItem, ApiBase>((t1, t2) => new object[]
                {
                    JoinType.Inner, t1.ApiBaseId == t2.ApiBaseId
                })
                .Where((t1, t2) => t1.ApiGroupId == apiGroupId)
                .OrderBy((t1, t2) => t1.RequestOrder)
                .Select((t1, t2) => new ApiGroupItemDto
                {
                    ApiGroupItemId = SqlFunc.GetSelfAndAutoFill(t1.ApiGroupItemId),
                    ApiName = t2.ApiName,
                    RequestPath = t2.RequestPath
                }).ToList();

            if (groupItems.Any())
            {
                var itemParamMappings = _dbContext.Integration.Queryable<ApiGroupItemParamMapping>()
                    .Where(t1 => t1.ApiGroupId == apiGroupId).ToList();
                foreach (var item in groupItems)
                {
                    var mappingDtos = itemParamMappings.Where(m => m.ApiGroupItemId == item.ApiGroupItemId)
                        .OrderBy(m => m.OrderIndex)
                        .ToList();

                    item.ParamMappings = mappingDtos;
                }
            }

            apiGroup.GroupItems = groupItems;

            apiGroup.ResultMappings = _dbContext.Integration.Queryable<ApiGroupResultMapping>()
                .Where(t1 => t1.ApiGroupId == apiGroupId)
                .OrderBy(t1 => t1.OrderIndex)
                .ToList();

            return apiGroup;
        }

        /// <summary>
        /// 获取Api请求参数集合
        /// </summary>
        /// <param name="apiBaseId">apiBaseId</param>
        /// <returns>请求参数集合</returns>
        public List<ApiRequestBodySchema> GetApiRequestBodySchemas(Guid? apiBaseId)
        {
            return _dbContext.Integration.Queryable<ApiRequestBodySchema>()
                .Where(t1 => t1.ApiBaseId == apiBaseId)
                .ToList();
        }

        /// <summary>
        /// 获取Api请求结果集
        /// </summary>
        /// <param name="apiBaseId">apiBaseId</param>
        /// <returns>请求结果集</returns>
        public List<ApiResponseSchemaDto> GetApiResponseSchemas(Guid? apiBaseId)
        {
            var responseSchemas = _dbContext.Integration.Queryable<ApiResultMapping>()
                .Where(t1 => t1.ApiBaseId == apiBaseId)
                .OrderBy(t1 => t1.OrderIndex)
                .Select(t1 => new ApiResponseSchemaDto
                {
                    SchemaId = t1.MappingId,
                    SchemaName = t1.SchemaName,
                    DataType = t1.DataType,
                    Required = t1.Required,
                    Description = t1.Description,
                    UpperSchemaId = t1.UpperMappingId,
                    DataSource = t1.DataSource
                })
                .ToList();

            if (responseSchemas.Count == 0 ||
                (responseSchemas.Count == 1 && string.IsNullOrEmpty(responseSchemas[0].DataSource)))
            {
                return _dbContext.Integration.Queryable<ApiResultSchema>()
                    .Where(t1 => t1.ApiBaseId == apiBaseId)
                    .OrderBy(t1 => t1.OrderIndex)
                    .Select(t1 => new ApiResponseSchemaDto
                    {
                        SchemaId = t1.SchemaId,
                        SchemaName = t1.SchemaName,
                        DataType = t1.DataType,
                        Required = t1.Required,
                        Description = t1.Description,
                        UpperSchemaId = t1.UpperSchemaId
                    })
                    .ToList();
            }

            return responseSchemas;
        }

        /// <summary>
        /// 下游API认证
        /// </summary>
        /// <param name="api"></param>
        /// <param name="downstreamApi"></param>
        /// <param name="request"></param>
        public void DownstreamApiAuth(Apis api, ServiceApis downstreamApi, HttpRequestMessage request)
        {
            if (downstreamApi == null)
            {
                var iQuery1 = _dbContext.Integration.Queryable<ServiceApis>();
                var iQuery2 = _dbContext.Integration.Queryable<ApiBase, ApisServiceMapping, ApisServiceMappingHttp, ApisServiceMappingWebService>((ab, asm, asmh, asmws) => new object[]
                {
                    JoinType.Inner, ab.ApiBaseId == asm.ApiBaseId,
                    JoinType.Left, asm.ServiceMappingId == asmh.ServiceMappingId,
                    JoinType.Left, asm.ServiceMappingId == asmws.ServiceMappingId
                }).Where(ab => ab.ApiId == api.ApiId)
                  .Select<object>((ab, asm, asmh, asmws) => new
                  {
                      ab.ApiBaseId,
                      asm.ServiceMappingId,
                      ServiceApiId = SqlFunc.IsNull(asmh.ServiceApiId, asmws.ServiceApiId),
                      ServiceId = SqlFunc.IsNull(asmh.ServiceId, asmws.ServiceId)
                  });

                downstreamApi = _dbContext.Integration.Queryable(iQuery1, iQuery2, JoinType.Inner, (a, b) => a.Id == SqlFunc.MappingColumn(default(Guid?), "b.ServiceApiId")
                    && a.ServiceId == SqlFunc.MappingColumn(default(Guid?), "b.ServiceId")).Select((a, b) => a).First();
            }

            if (downstreamApi == null)
            {
                return;
            }

            var service = _dbContext.Integration.Queryable<Services>().InSingle(downstreamApi.ServiceId);

            if (service != null && !string.IsNullOrEmpty(service.AuthenticationMode) && service.AuthenticationMode != "None")
            {
                // Identity Server 4 - 客户端模式
                if (service.AuthenticationMode == "Ids4ClientCredentials")
                {
                    Ids4ClientCredentials(request, new IdentityServer4AuthRequestDto()
                    {
                        TokenApi = service.TokenApi,
                        ClientId = service.Ids4ClientId,
                        ClientSecret = service.Ids4ClientSecret,
                        ServiceCode = service.ServiceCode
                    });
                }

                // 参数签名验证 - MD5
                if (service.AuthenticationMode == "ParamSignMd5")
                {
                    var authParams = _dbContext.Integration.Queryable<ServiceAuthParam>().Where(r => r.ServiceId == service.ServiceId)
                        .ToList();

                    RequestParamSignMd5(request, new ParamSignMd5Dto()
                    {
                        AuthParams = authParams,
                        DataFormat = downstreamApi.DataFormat,
                        RequestMethod = downstreamApi.RequestMethod,
                        SignKey = service.Signkey,
                        SignSecret = service.SignSecret
                    });
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="api"></param>
        /// <param name="responseValue"></param>
        public void ExecCustomSuccess(Apis api, string responseValue)
        {
            var apiResult = _dbContext.Integration.Queryable<Apis, ApiBase, ApiResultSample>((a, ab, ars) => new object[]
            {
                JoinType.Inner, a.ApiId == ab.ApiId,
                JoinType.Inner, ab.ApiBaseId == ars.ApiBaseId
            }).Where((a, ab, ars) => a.ApiId == api.ApiId && ars.ApiSuccessDefine == "expression")
              .Select((a, ab, ars) => ars)
              .First();

            if (apiResult != null)
            {
                var jToken = JsonConvert.DeserializeObject<JToken>(responseValue);
                var value = apiResult.DefineSchemaKeys.Split('|')
                    .Select(r => r.Split('#')[1])
                    .Aggregate(jToken, (current, schemaName) => current[schemaName]);

                var target = new DynamicExpresso.Interpreter();
                var express = $"{value} {apiResult.DefineJudge} {apiResult.DefineValue}";
                bool isSuccess = target.Eval<bool>(express);
                if (!isSuccess)
                {
                    if (apiResult.ApiMsgDefine == "expression")
                    {
                        var msgValue = apiResult.DefineMsgValue.Split('|')
                          .Select(r => r.Split('#')[1])
                          .Aggregate(jToken, (current, schemaName) => current[schemaName]);

                        throw new Exception("下游接口错误：" + msgValue);
                    }

                    throw new Exception("下游接口错误");
                }
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 判断结果是否映射
        /// </summary>
        /// <param name="mappings">结果映射</param>
        /// <returns>结果是否映射</returns>
        private bool ResultIsMapper(List<ApiResultDesignMappingDto> mappings)
        {
            if (mappings == null)
            {
                return false;
            }

            // 获取根节点
            var mapperRoot = mappings.Find(r => !r.UpperMappingId.HasValue);
            if (mapperRoot == null)
            {
                return false;
            }

            if (mappings.Any(r => !string.IsNullOrEmpty(r.DataSource)))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 获取下游参数映射参数
        /// </summary>
        /// <param name="api">api对象</param>
        /// <param name="postValue">请求数据</param>
        /// <returns>Mapping后下游参数</returns>
        private (Dictionary<string, object>, ServiceApis) GetDownstreamParamsMapping(Apis api, JToken postValue)
        {
            var dictMapping = new Dictionary<string, object>();

            // 下游API
            var iQuery1 = _dbContext.Integration.Queryable<ServiceApis>();
            var iQuery2 = _dbContext.Integration.Queryable<ApiBase, ApisServiceMapping, ApisServiceMappingHttp, ApisServiceMappingWebService>((ab, asm, asmh, asmws) => new object[]
             {
                JoinType.Inner, ab.ApiBaseId == asm.ApiBaseId,
                JoinType.Left, asm.ServiceMappingId == asmh.ServiceMappingId,
                JoinType.Left, asm.ServiceMappingId == asmws.ServiceMappingId
             }).Where(ab => ab.ApiId == api.ApiId)
               .Select<object>((ab, asm, asmh, asmws) => new
               {
                   ab.ApiBaseId,
                   asm.ServiceMappingId,
                   ServiceApiId = SqlFunc.IsNull(asmh.ServiceApiId, asmws.ServiceApiId),
                   ServiceId = SqlFunc.IsNull(asmh.ServiceId, asmws.ServiceId)
               });

            var downstreamApi = _dbContext.Integration.Queryable(iQuery1, iQuery2, JoinType.Inner, (a, b) => a.Id == SqlFunc.MappingColumn(default(Guid?), "b.ServiceApiId")
                && a.ServiceId == SqlFunc.MappingColumn(default(Guid?), "b.ServiceId")).Select((a, b) => a).First();

            // 请求参数
            var requestParams = _dbContext.Integration.Queryable<ApiBase, ApiRequestBodySchema>((ab, rs) => new object[]
            {
                JoinType.Inner, ab.ApiBaseId == rs.ApiBaseId
            }).Where((ab, rs) => ab.ApiId == api.ApiId)
              .Select((ab, rs) => new ApiResultSchema()
              {
                  SchemaId = rs.SchemaId,
                  SchemaName = rs.SchemaName,
                  DataType = rs.DataType,
                  Description = rs.Description,
                  UpperSchemaId = rs.UpperSchemaId
              }).ToList();

            // 映射参数
            var backendDesignParams = _dbContext.Integration.Queryable<ApiBase, ApiBackendDesignParamDto>((ab, dp) => new object[]
            {
                JoinType.Inner, ab.ApiBaseId == dp.ApiBaseId
            }).Where((ab, dp) => ab.ApiId == api.ApiId)
              .Select((ab, dp) => dp)
              .ToList();
            backendDesignParams.ForEach(r =>
            {
                if (r.DataSourceType == MappingDataSource.RequestBody.ToOurString() && !string.IsNullOrEmpty(r.DataSource))
                {
                    r.RequestSchemaPath = new List<string>();
                    var schema = requestParams.Find(x => x.SchemaId == r.DataSource.ToOurGuid());
                    while (schema != null)
                    {
                        if (schema.SchemaName != "root")
                        {
                            r.RequestSchemaPath.Add(schema.SchemaName);
                        }

                        schema = requestParams.Find(x => x.SchemaId == schema.UpperSchemaId);
                    }

                    r.RequestSchemaPath.Reverse();
                }
            });

            var paramLocations = backendDesignParams.Select(r => r.ParamLocation).Distinct().ToList();

            paramLocations.ForEach(pl =>
            {
                var locationParams = backendDesignParams.Where(r => r.ParamLocation == pl).OrderBy(r => r.OrderIndex).ToList();
                dictMapping[pl] = new Dictionary<string, object>();

                if (pl == "Body" && (downstreamApi == null
                    || (downstreamApi != null && downstreamApi.RequestMethod.ToUpper() == "POST"
                        && (downstreamApi.DataFormat?.ToLower() ?? "json") == "json")))
                {
                    var requestMappingParams = locationParams.Select(x => new ApiResultDesignMappingDto()
                    {
                        MappingId = x.ParamId,
                        SchemaName = x.ParamName,
                        DataType = x.DataType,
                        Description = x.Description,
                        UpperMappingId = x.UpperParamId,
                        DataSource = x.DataSourceType,
                        DataSourceValue = x.DataSource,
                        MappingSchemaPath = x.RequestSchemaPath
                    }).ToList();
                    var isMapper = ResultIsMapper(requestMappingParams);
                    var requestMappingResponse = GetApiResponseSchemas(requestParams, requestMappingParams);
                    dictMapping[pl] = requestMappingResponse.ToMappingResponse(postValue, isMapper);
                }
                else
                {
                    var rootMappingResponse = new List<ApiResponseSchemaDto>()
                    {
                        new ApiResponseSchemaDto()
                        {
                            SchemaId = Guid.NewGuid(),
                            SchemaName = "root",
                            DataType = "object"
                        }
                    };

                    var requestMappingResponse = locationParams.Select(x => new ApiResponseSchemaDto()
                    {
                        SchemaId = x.ParamId,
                        SchemaName = x.ParamName,
                        DataType = x.DataType,
                        Description = x.Description,
                        UpperSchemaId = rootMappingResponse[0].SchemaId,
                        DataSource = x.DataSourceType,
                        SchemaPaths = x.RequestSchemaPath,
                        FixedValue = x.DataSourceType == MappingDataSource.FixedValue.ToOurString() ? x.DataSource : null
                    }).Concat(rootMappingResponse).ToList();

                    dictMapping[pl] = JsonConvert.DeserializeObject<Dictionary<string, object>>(JsonConvert.SerializeObject(requestMappingResponse.ToMappingResponse(postValue, true)));
                }
            });

            return (dictMapping, downstreamApi);
        }

        /// <summary>
        /// 获取API返回结构
        /// </summary>
        /// <param name="schemas">API返回原始结构</param>
        /// <param name="mappingSchemas">映射结构</param>
        /// <returns>结构</returns>
        private List<ApiResponseSchemaDto> GetApiResponseSchemas(List<ApiResultSchema> schemas, List<ApiResultDesignMappingDto> mappingSchemas)
        {
            var responseSchemas = schemas.OrderBy(x => x.OrderIndex)
                                .Select(x => new ApiResponseSchemaDto
                                {
                                    SchemaId = x.SchemaId.Value,
                                    SchemaName = x.SchemaName,
                                    DataType = x.DataType,
                                    Description = x.Description,
                                    UpperSchemaId = x.UpperSchemaId
                                }).ToList();

            if (ResultIsMapper(mappingSchemas))
            {
                responseSchemas = mappingSchemas.OrderBy(x => x.OrderIndex)
                    .Select(x => new ApiResponseSchemaDto
                    {
                        SchemaId = x.MappingId.Value,
                        SchemaName = x.SchemaName,
                        DataType = x.DataType,
                        Description = x.Description,
                        UpperSchemaId = x.UpperMappingId,
                        DataSource = x.DataSource,
                        SchemaPaths = x.MappingSchemaPath,
                        FixedValue = x.DataSource == MappingDataSource.FixedValue.ToOurString() ? x.DataSourceValue : null
                    }).ToList();

                // Object、Array 下没有映射结构，拿返回结构
                mappingSchemas.ForEach(r =>
                {
                    if (r.DataType.ToLower() == "object" || r.DataType.ToLower() == "array")
                    {
                        if (!string.IsNullOrEmpty(r.DataSource) && r.DataSource != MappingDataSource.FixedValue.ToOurString() && !mappingSchemas.Any(x => x.UpperMappingId == r.MappingId))
                        {
                            var insertMappingSchemas = schemas.Where(x => x.UpperSchemaId == r.DataSourceValue.ToOurGuid())
                                .Select(x => new ApiResponseSchemaDto
                                {
                                    SchemaId = Guid.NewGuid(),
                                    SchemaName = x.SchemaName,
                                    DataType = x.DataType,
                                    Description = x.Description,
                                    UpperSchemaId = r.MappingId
                                }).ToList();

                            responseSchemas.AddRange(insertMappingSchemas);
                        }
                    }
                });
            }

            return responseSchemas;
        }

        /// <summary>
        /// 提取SQL参数
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <returns>SQL参数</returns>
        private List<string> ExtractSqlParams(string sql)
        {
            Regex regex = new Regex(@"\{[^\}]+\}");
            var matchParams = regex.Matches(sql);

            return matchParams.Select(r => r.Value).Distinct().ToList();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dataFormat"></param>
        /// <returns></returns>
        private string GetPostContentType(string dataFormat)
        {
            string contentType = "application/json";

            switch (dataFormat)
            {
                case "json":
                    contentType = "application/json";
                    break;
                case "x-www-form-urlencoded":
                    contentType = "application/x-www-form-urlencoded";
                    break;
                case "form-data":
                    contentType = "multipart/form-data";
                    break;
            }

            return contentType;
        }

        #region 下游接口认证

        #region 参数签名MD5认证

        /// <summary>
        /// 参数签名MD5认证
        /// </summary>
        /// <param name="request">请求体</param>
        /// <param name="md5Dto"></param>
        private void RequestParamSignMd5(HttpRequestMessage request, ParamSignMd5Dto md5Dto)
        {
            if (md5Dto.AuthParams.Any())
            {
                md5Dto.AuthParams.ForEach(r =>
                {
                    if (r.ParamLocation == "Header")
                    {
                        if (r.MappingType == "FixedValue")
                        {
                            request.Headers.Add(r.ParamName, r.MappingValue);
                        }

                        if (r.MappingType == "ApiToken")
                        {
                            switch (r.MappingValue)
                            {
                                case "Key":
                                    request.Headers.Add(r.ParamName, md5Dto.SignKey);
                                    break;
                                case "Secret":
                                    request.Headers.Add(r.ParamName, md5Dto.SignSecret);
                                    break;
                                case "Sign":
                                    request.Headers.Add(r.ParamName, GetParamMd5Sign(request, md5Dto));
                                    break;
                            }
                        }
                    }
                });
            }
        }

        private string GetParamMd5Sign(HttpRequestMessage request, ParamSignMd5Dto md5Dto)
        {
            string paramString = string.Empty;
            List<string> liFilterParams = new List<string>();
            var sdictParams = new SortedDictionary<string, string>()
            {
                { "appKey", md5Dto.SignKey }
            };

            List<string> liParams = new List<string>() { $"appKey={md5Dto.SignKey}" };

            if (md5Dto.RequestMethod.ToUpper() == "GET")
            {
                liParams = liParams.Concat(request.RequestUri.Query.TrimStart('?').Split('&').ToList()).ToList();
                request.RequestUri.Query.TrimStart('?').Split('&').ToList().ForEach(r =>
                {
                    var keyValue = r.Split('=');
                    sdictParams[keyValue[0]] = keyValue[1];
                });
            }

            if (md5Dto.RequestMethod.ToUpper() == "POST")
            {
                if ((md5Dto.DataFormat ?? "json") == "json")
                {
                    var dictParam = JsonConvert.DeserializeObject<SortedDictionary<string, string>>(request.Content.ReadAsStringAsync().Result);
                    foreach (var item in dictParam)
                    {
                        liParams.Add($"{item.Key}={item.Value}");
                    }
                }

                if (md5Dto.DataFormat == "x-www-form-urlencoded")
                {
                    liParams = liParams.Concat(request.Content.ReadAsStringAsync().Result.Split('&').ToList()).ToList();
                    request.Content.ReadAsStringAsync().Result.Split('&').ToList().ForEach(r =>
                    {
                        var keyValue = r.Split('=');
                        sdictParams[keyValue[0]] = keyValue[1];
                    });
                }
            }

            paramString = string.Join("&", sdictParams.Select(r => $"{r.Key}={r.Value}")) + md5Dto.SignSecret;

            string md5 = CryptoExtension.Md5(paramString);

            return md5;
        }

        #endregion

        #region Identity Server 4 客户端模式

        /// <summary>
        /// Identity Server 4 客户端模式
        /// </summary>
        /// <param name="request"></param>
        /// <param name="authRequestDto"></param>
        private void Ids4ClientCredentials(HttpRequestMessage request, IdentityServer4AuthRequestDto authRequestDto)
        {
            using (HttpClient httpClient = new HttpClient())
            {
                string client_id = "client_id";
                string client_secret = "client_secret";
                if (ids4Mapping != null && ids4Mapping[authRequestDto.ServiceCode] != null)
                {
                    client_id = ids4Mapping[authRequestDto.ServiceCode]["client_id"]?.ToString() ?? "client_id";
                    client_secret = ids4Mapping[authRequestDto.ServiceCode]["client_secret"]?.ToString() ?? "client_secret";
                }

                Dictionary<string, string> dicParam = new Dictionary<string, string>()
                {
                    { client_id, authRequestDto.ClientId },
                    { client_secret, authRequestDto.ClientSecret },
                    { "grant_type", "client_credentials" }
                };
                var httpContent = new FormUrlEncodedContent(dicParam);
                using (var httpResponse = httpClient.PostAsync(authRequestDto.TokenApi, httpContent))
                {
                    httpResponse.Wait();

                    var result = httpResponse.Result.Content.ReadAsStringAsync();
                    result.Wait();

                    var ids4Result = JsonConvert.DeserializeObject<IdentityServer4TokenDto>(result.Result);
                    if (!string.IsNullOrEmpty(ids4Result.AccessToken))
                    {
                        request.Headers.Authorization = new AuthenticationHeaderValue(ids4Result.TokenType.FirstCharToUpper(), ids4Result.AccessToken);
                    }
                }
            }
        }

        #endregion

        #endregion

        #endregion
    }
}
