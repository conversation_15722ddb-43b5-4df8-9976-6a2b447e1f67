using MT.Enterprise.Core.ORM;
using SqlSugar;

namespace Medusa.Service.Ocelot.ApiHost.Core.ORM
{
    /// <summary>
    /// DbContext 访问类
    /// </summary>
    public class MyDbContext : DbContextBase
    {
        /// <summary>
        /// MyDbContext
        /// </summary>
        /// <param name="dbContext">dbContext</param>
        public MyDbContext(IDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// MySql
        /// </summary>
        /// <param name="connection">MY SQL连接字符串</param>
        /// <returns>My Sql Clent</returns>
        public SqlSugarClient MySqlClient(string connection)
        {
            SqlSugarClient db = new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = connection,
                DbType = DbType.MySql,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.SystemTable
            });

            return db;
        }

        /// <summary>
        /// SqlServer
        /// </summary>
        /// <param name="connection">Sql Server 连接字符串</param>
        /// <returns>SQL SERVER Client</returns>
        public SqlSugarClient SqlServerClient(string connection)
        {
            SqlSugarClient db = new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = connection,
                DbType = DbType.SqlServer,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.SystemTable
            });

            return db;
        }
    }
}
