using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration
{
    /// <summary>
    /// ApiGroupPublishInfo
    /// </summary>
    [EntityTable("ApiGroupPublishInfo", "Api组合发布信息")]
    public class ApiGroupPublishInfo
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? InnerId { get; set; }

        /// <summary>
        /// Api组合请求和返回结构
        /// </summary>
        [EntityColumn(ColumnDescription = "Api组合请求和返回结构", ColumnDataType = "varchar(max),text")]
        public string ApiGroupSchemaJson { get; set; }

        /// <summary>
        /// 最新版本号
        /// </summary>
        [EntityColumn(ColumnDescription = "最新版本号", ColumnDataType = "varchar(50)")]
        public string VersionNo { get; set; }

        /// <summary>
        /// 发布说明
        /// </summary>
        [EntityColumn(ColumnDescription = "发布说明", ColumnDataType = "varchar(2000)")]
        public string PublishDesc { get; set; }

        /// <summary>
        /// Api组合主键Id
        /// </summary>
        [EntityColumn(ColumnDescription = "Api组合主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ApiGroupId { get; set; }
    }
}
