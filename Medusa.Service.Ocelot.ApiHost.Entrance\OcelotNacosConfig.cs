﻿using System.IO;

namespace Medusa.Service.Ocelot.ApiHost.Entrance
{
    public class OcelotNacosConfig
    {
        public string Host { get; set; }

        public string DataId { get; set; }

        public string Group { get; set; }

        public string Tenant { get; set; }

        public string Type { get; set; }

        public string GetNacosConfig
        {
            get
            {
                if (string.IsNullOrEmpty(Host)) return "";
                return $"{Host}/nacos/v1/cs/configs";
            }
        }

        public string PublishNacosConfig
        {
            get
            {
                if (string.IsNullOrEmpty(Host)) return "";
                return $"{Host}/nacos/v1/cs/configs";
            }
        }
    }
}
