using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration
{
    /// <summary>
    /// ApiGroupResultSample
    /// </summary>
    [EntityTable("ApiGroupResultSample", "Api组合结果样例")]
    public class ApiGroupResultSample
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? InnerId { get; set; }

        /// <summary>
        /// 样例数据类型
        /// </summary>
        [EntityColumn(ColumnDescription = "样例数据类型", ColumnDataType = "varchar(50)")]
        public string ResultSampleType { get; set; }

        /// <summary>
        /// 样例值
        /// </summary>
        [EntityColumn(ColumnDescription = "样例值", ColumnDataType = "varchar(max),text")]
        public string DataValue { get; set; }

        /// <summary>
        /// Api组合主键Id
        /// </summary>
        [EntityColumn(ColumnDescription = "Api组合主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ApiGroupId { get; set; }
    }
}
