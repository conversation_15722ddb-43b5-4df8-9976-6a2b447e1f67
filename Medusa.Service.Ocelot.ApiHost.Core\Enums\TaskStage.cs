﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Core.Enums
{
    /// <summary>
    /// 任务阶段
    /// </summary>
    public enum TaskStage
    {
        /// <summary>
        /// 数据接收-开始
        /// </summary>
        [Description("数据接收-开始")]
        ReceiveStart,

        /// <summary>
        /// 数据接收-数据过滤-开始
        /// </summary>
        [Description("数据接收-数据过滤-开始")]
        ReceiveDataFilterStart,

        /// <summary>
        /// 数据接收-数据过滤-结束
        /// </summary>
        [Description("数据接收-数据过滤-结束")]
        ReceiveDataFilterEnd,

        /// <summary>
        /// 数据接收-预处理-开始
        /// </summary>
        [Description("数据接收-预处理-开始")]
        ReceivePreHandleStart,

        /// <summary>
        /// 数据接收-预处理-结束
        /// </summary>
        [Description("数据接收-预处理-结束")]
        ReceivePreHandleEnd,

        /// <summary>
        /// 数据接收-结束
        /// </summary>
        [Description("数据接收-结束")]
        ReceiveEnd,

        /// <summary>
        /// 数据分发-开始
        /// </summary>
        [Description("数据分发-开始")]
        DistributionStart,

        /// <summary>
        /// 数据分发-数据过滤-开始
        /// </summary>
        [Description("数据分发-数据过滤-开始")]
        DistributionDataFilterStart,

        /// <summary>
        /// 数据分发-数据过滤-结束
        /// </summary>
        [Description("数据分发-数据过滤-结束")]
        DistributionDataFilterEnd,

        /// <summary>
        /// 数据分发-预处理-开始
        /// </summary>
        [Description("数据分发-预处理-开始")]
        DistributionPreHandleStart,

        /// <summary>
        /// 数据分发-预处理-结束
        /// </summary>
        [Description("数据分发-预处理-结束")]
        DistributionPreHandleEnd,

        /// <summary>
        /// 数据分发-结束
        /// </summary>
        [Description("数据分发-结束")]
        DistributionEnd
    }
}
