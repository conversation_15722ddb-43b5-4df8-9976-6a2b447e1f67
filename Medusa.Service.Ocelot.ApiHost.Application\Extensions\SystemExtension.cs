﻿using System;
using System.Collections.Generic;
using System.IO;
using Medusa.Service.Ocelot.ApiHost.Application.Dtos;
using Newtonsoft.Json;

namespace Medusa.Service.Ocelot.ApiHost.Application.Extensions
{
    /// <summary>
    /// System扩展类
    /// </summary>
    public static class SystemExtension
    {
        /// <summary>
        /// 转实体
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="dictEntity">字典对象</param>
        /// <returns>实体对象</returns>
        public static T ToEntity<T>(this Dictionary<string, object> dictEntity)
        {
            if (dictEntity == null)
            {
                return default(T);
            }

            return JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(dictEntity));
        }

        /// <summary>
        /// 转字典对象
        /// </summary>
        /// <param name="entity">实体对象</param>
        /// <returns>字典对象</returns>
        public static Dictionary<string, object> ToDictionary(this object entity)
        {
            if (entity == null)
            {
                return new Dictionary<string, object>();
            }

            return JsonConvert.DeserializeObject<Dictionary<string, object>>(JsonConvert.SerializeObject(entity));
        }

        /// <summary>
        /// 转字典对象
        /// </summary>
        /// <param name="objectJson">实体对象JSON</param>
        /// <returns>字典对象</returns>
        public static Dictionary<string, object> JsonToDictionary(this string objectJson)
        {
            if (string.IsNullOrEmpty(objectJson))
            {
                return new Dictionary<string, object>();
            }

            return JsonConvert.DeserializeObject<Dictionary<string, object>>(objectJson);
        }

        /// <summary>
        /// 转 System Type
        /// </summary>
        /// <param name="typeLower">类型</param>
        /// <returns>System Type</returns>
        public static Type ToSystemType(this string typeLower)
        {
            Type type = typeof(string);
            switch (typeLower)
            {
                case "intger": type = typeof(int); break;
                case "boolean": type = typeof(bool); break;
                case "number": type = typeof(decimal); break;
                case "object": type = typeof(Dictionary<string, MapperPathDto>); break;
                case "array": type = typeof(List<Dictionary<string, MapperPathDto>>); break;
            }

            return type;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T">实体</typeparam>
        /// <param name="arrJson">arrJson</param>
        /// <returns>返回实体集合</returns>
        public static List<T> JsonToList<T>(this string arrJson)
        {
            if (string.IsNullOrEmpty(arrJson))
            {
                return new List<T>();
            }

            return JsonConvert.DeserializeObject<List<T>>(arrJson);
        }

        /// <summary>
        /// 将 Stream 转成 byte[] 
        /// </summary>
        /// <param name="stream">stream</param>
        /// <returns>返回字体</returns>
        public static byte[] ToBytes(this Stream stream)
        {
            byte[] bytes = new byte[stream.Length];
            stream.Read(bytes, 0, bytes.Length);

            // 设置当前流的位置为流的开始 
            stream.Seek(0, SeekOrigin.Begin);
            return bytes;
        }

        /// <summary>
        /// 将 bytes[] 转成 Stream
        /// </summary>
        /// <param name="bytes">bytes</param>
        /// <returns>Stream</returns>
        public static Stream ToStream(this byte[] bytes)
        {
            Stream stream = new MemoryStream(bytes);

            return stream;
        }

        /// <summary>
        /// string转枚举
        /// </summary>
        /// <typeparam name="T">枚举</typeparam>
        /// <param name="value">value</param>
        /// <returns>枚举</returns>
        public static T ToEnum<T>(this string value)
        {
            return (T)System.Enum.Parse(typeof(T), value);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string FirstCharToUpper(this string str)
        {
            if (string.IsNullOrEmpty(str))
            {
                return str;
            }

            if (str.Length > 1)
            {
                return char.ToUpper(str[0]) + str.Substring(1);
            }

            return str.ToUpper();
        }
    }
}
