﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <UserSecretsId>5d69fd81-e69d-4870-a994-8ba5078c6d64</UserSecretsId>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>bin\Debug\netcoreapp3.1\Medusa.Service.Ocelot.ApiHost.Entrance.xml</DocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DocumentationFile>bin\Debug\netcoreapp3.1\Medusa.Service.Ocelot.ApiHost.Entrance.xml</DocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="IdentityServer4.AccessTokenValidation" Version="3.0.1" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="5.2.7" />
    <PackageReference Include="Microsoft.AspNetCore.Cors" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.23" />
    <PackageReference Include="nacos-sdk-csharp-unofficial.AspNetCore" Version="0.8.5" />
    <PackageReference Include="Ocelot.Provider.Nacos" Version="1.0.0" />
    <PackageReference Include="Ocelot.Provider.Polly" Version="16.0.1" />
    <PackageReference Include="Serilog" Version="2.10.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="5.5.1" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.8.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="3.1.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\mt.enterprise.tools.shuiwu\src\Pretreatment\MT.Pretreatment.csproj" />
    <ProjectReference Include="..\Medusa.Service.Ocelot.ApiHost.Application\Medusa.Service.Ocelot.ApiHost.Application.csproj" />
    <ProjectReference Include="..\Medusa.Service.Ocelot.ApiHost.Core\Medusa.Service.Ocelot.ApiHost.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Dockerfile">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ProjectExtensions><VisualStudio><UserProperties appsettings_1json__JsonSchema="" /></VisualStudio></ProjectExtensions>


</Project>
