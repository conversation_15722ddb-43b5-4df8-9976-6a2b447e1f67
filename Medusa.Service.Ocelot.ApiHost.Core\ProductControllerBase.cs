﻿using System;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Ocelot.ApiHost.Core
{
    /// <summary>
    /// Product controller base.
    /// </summary>
    public class ProductControllerBase : ControllerBase
    {
        /// <summary>
        /// ProductControllerBase
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        public ProductControllerBase(IServiceProvider serviceProvider)
        {
            // var productRegistrationService = serviceProvider.GetService<IProductRegistrationService>();
            // if (productRegistrationService.GetProductLicense() == null)
            // {
            //     throw new StatusNotExtendedException(I18nManager.GetString("NotRegistered"));
            // }
        }
    }
}
