﻿using Medusa.Service.Ocelot.ApiHost.Core.Dto.Ocelot;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Utils.Extensions;
using Nacos;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Ocelot.Configuration.Creator;
using Ocelot.Configuration.File;
using Ocelot.Configuration.Repository;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;

namespace Medusa.Service.Ocelot.ApiHost.Entrance.Extensions
{
    /// <summary>
    /// 
    /// </summary>
    public static class OcelotNacosExtensions
    {
        /// <summary>
        /// 添加Ocelot配置中心
        /// </summary>
        /// <param name="services"></param>
        /// <param name="nacosOcelotData"></param>
        /// <returns></returns>
        public static IConfiguration AddOcelotNacosConfig(this IServiceCollection services, JToken nacosOcelotData)
        {
            var serviceProvider = services.BuildServiceProvider();
            var configClient = serviceProvider.GetService<INacosConfigClient>();

            var ocelotConfiguration = configClient.LoadOcelotConfig(nacosOcelotData);

            return ocelotConfiguration;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="configClient"></param>
        /// <param name="nacosOcelotData"></param>
        /// <returns></returns>
        public static string GetOcelotConfig(this INacosConfigClient configClient, JToken nacosOcelotData)
        {
            try
            {
                GetConfigRequest configRequest = nacosOcelotData.ToString().ToOurObject<GetConfigRequest>();
                if (configRequest == null || string.IsNullOrEmpty(configRequest.DataId))
                {
                    throw new Exception("appsetting.json not found \"NacosOcelotData\" node");
                }

                var ocelotConfigJsonTask = configClient.GetConfigAsync(configRequest);
                ocelotConfigJsonTask.Wait();

                var ocelotConfigJson = ocelotConfigJsonTask.Result;
                if (string.IsNullOrEmpty(ocelotConfigJson))
                {
                    throw new Exception("ocelot config empty");
                }

                return ocelotConfigJson;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// 发布Ocelot Config
        /// </summary>
        /// <param name="configClient"></param>
        /// <param name="nacosOcelotData"></param>
        /// <param name="fileConfigurationExtDto"></param>
        [Obsolete]
        public static void PublishOcelotConfig(INacosConfigClient configClient, JToken nacosOcelotData, FileConfigurationExtDto fileConfigurationExtDto)
        {
            PublishConfigRequest configRequest = nacosOcelotData.ToString().ToOurObject<PublishConfigRequest>();
            if (configRequest == null || string.IsNullOrEmpty(configRequest.DataId))
            {
                throw new Exception("appsetting.json not found \"NacosOcelotData\" node");
            }

            configRequest.Type = "json";
            configRequest.Content = Newtonsoft.Json.JsonConvert.SerializeObject(fileConfigurationExtDto);

            var publishTask = configClient.PublishConfigAsync(configRequest);
            publishTask.Wait();
        }

        /// <summary>
        /// 发布Ocelot Config
        /// </summary>
        /// <param name="nacosConfig"></param>
        /// <param name="fileConfigurationExtDto"></param>
        public static void PublishOcelotConfig(OcelotNacosConfig nacosConfig, FileConfigurationExtDto fileConfigurationExtDto)
        {
            using (HttpClient httpClient = new HttpClient())
            {
                using (var content = new MultipartFormDataContent())
                {
                    var routes = JsonConvert.SerializeObject(fileConfigurationExtDto);

                    Console.WriteLine($"NacosConfig -> {JsonConvert.SerializeObject(nacosConfig)}");
                    Console.WriteLine($"MultipartFormDataContent -> Content -> {routes}");

                    content.Add(new StringContent(nacosConfig.DataId), "dataId");
                    content.Add(new StringContent(nacosConfig.Group), "group");
                    content.Add(new StringContent(nacosConfig.Tenant), "tenant");
                    content.Add(new StringContent(nacosConfig.Type), "type");
                    content.Add(new StringContent(routes), "content");                    

                    var publishTask = httpClient.PostAsync(nacosConfig.PublishNacosConfig, content);
                    publishTask.Wait();
                }
            }
        }

        /// <summary>
        /// 监听Ocelot配置
        /// </summary>
        /// <param name="appBuilder"></param>
        /// <param name="nacosOcelotData"></param>
        public static void UseListenerOcelotConfig(this IApplicationBuilder appBuilder, string nacosOcelotData)
        {
            var listenerRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<AddListenerRequest>(nacosOcelotData);
            listenerRequest.Callbacks = new List<Action<string>>()
            {
                x =>
                {
                    Console.WriteLine(x);
                    var ocelotConfig = NacosDataToOcelotConfig(x);
                    SetOcelotConfig(appBuilder, ocelotConfig);
                }
            };

            var configClient = appBuilder.ApplicationServices.GetService<INacosConfigClient>();
            configClient.AddListenerAsync(listenerRequest);
        }

        /// <summary>
        /// 加载Ocelot配置
        /// </summary>
        /// <param name="configClient"></param>
        /// <param name="nacosOcelotData"></param>
        private static IConfiguration LoadOcelotConfig(this INacosConfigClient configClient, JToken nacosOcelotData)
        {
            try
            {
                var ocelotConfigJson = GetOcelotConfig(configClient, nacosOcelotData);

                MemoryStream stream = new MemoryStream(ocelotConfigJson.ToOurBytes());
                var configBuilder = new ConfigurationBuilder();
                configBuilder.AddJsonStream(stream);

                return configBuilder.Build();
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// 更新Ocelot配置
        /// </summary>
        /// <param name="appBuilder"></param>
        /// <param name="fileConfiguration"></param>
        private static void SetOcelotConfig(this IApplicationBuilder appBuilder, FileConfiguration fileConfiguration)
        {
            var internalConfigCreator = appBuilder.ApplicationServices.GetService<IInternalConfigurationCreator>();
            var taskResponse = internalConfigCreator.Create(fileConfiguration);
            taskResponse.Wait();

            var internalConfigRepostitory = appBuilder.ApplicationServices.GetService<IInternalConfigurationRepository>();
            internalConfigRepostitory.AddOrReplace(taskResponse.Result.Data);
        }

        /// <summary>
        /// Nacos 配置转为ocelot对象的路由
        /// </summary>
        /// <param name="nacosData"></param>
        /// <returns></returns>
        private static FileConfiguration NacosDataToOcelotConfig(string nacosData)
        {
            if (string.IsNullOrEmpty(nacosData))
            {
                throw new Exception("Route can't be empty");
            }

            return Newtonsoft.Json.JsonConvert.DeserializeObject<FileConfiguration>(nacosData);
        }
    }
}
