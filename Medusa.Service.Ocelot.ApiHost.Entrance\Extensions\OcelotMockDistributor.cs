﻿using Medusa.Service.Ocelot.ApiHost.Application.Dtos;
using Medusa.Service.Ocelot.ApiHost.Application.OcelotApi;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration;
using Serilog;
using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Medusa.Service.Ocelot.ApiHost.Entrance.Extensions
{
    /// <summary>
    /// 
    /// </summary>
    public class OcelotMockDistributor : DelegatingHandler
    {
        private readonly IOcelotApiSerivce _thirdProvideSerivce;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="thirdProvideSerivce"></param>
        public OcelotMockDistributor(IOcelotApiSerivce thirdProvideSerivce)
        {
            _thirdProvideSerivce = thirdProvideSerivce;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected override  Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            try
            {
                var apiToken = request.Headers.GetValues("api-token").ToList().FirstOrDefault();
                Apis api = _thirdProvideSerivce.GetApis(apiToken);

                var dto = _thirdProvideSerivce.GetInterfaceMockInfo(api);

                return  OcelotExtension.ResponseTaskAsync(dto);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "OcelotMockDistributor");
                return OcelotExtension.ResponseTaskAsync(new OcelotResponseDto() { ErrorCode = ((int)HttpStatusCode.InternalServerError).ToString(), ErrorMessage = ex.Message });
            }
        }
    }
}
