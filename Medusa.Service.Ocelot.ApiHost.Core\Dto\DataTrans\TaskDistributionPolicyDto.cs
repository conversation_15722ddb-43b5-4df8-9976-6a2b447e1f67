﻿using System;

namespace Medusa.Service.Ocelot.ApiHost.Core.Dtos
{
    /// <summary>
    /// 任务-数据分发策略
    /// </summary>
    public class TaskDistributionPolicyDto
    {
        /// <summary>
        /// 分发API
        /// </summary>
        public string DistributionApi { get; set; }

        /// <summary>
        /// 是否分页
        /// </summary>
        public bool? IsPaging { get; set; } = false;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int? PageSize { get; set; }

        /// <summary>
        /// 推送通知API
        /// </summary>
        public string NoticeApi { get; set; }

        /// <summary>
        /// 分发Id
        /// </summary>
        public Guid? DistributionId { get; set; }

        /// <summary>
        /// 分发名
        /// </summary>
        public string Dispenser { get; set; }

        /// <summary>
        /// 数据处理策略
        /// </summary>
        public TaskDistributionDataProcessDto DataProcess { get; set; }
    }
}
