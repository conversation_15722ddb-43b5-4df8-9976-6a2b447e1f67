﻿using Medusa.Service.Ocelot.ApiHost.Application.Dtos;
using Medusa.Service.Ocelot.ApiHost.Application.OcelotApi;
using Medusa.Service.Ocelot.ApiHost.Core;
using Medusa.Service.Ocelot.ApiHost.Core.Constants;
using Medusa.Service.Ocelot.ApiHost.Core.Dto.Ocelot;
using Medusa.Service.Ocelot.ApiHost.Core.Dtos;
using Medusa.Service.Ocelot.ApiHost.Core.Extensions;
using Medusa.Service.Ocelot.ApiHost.Entrance.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Utils;
using Nacos;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Medusa.Service.Ocelot.ApiHost.Entrance.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [ApiController]
    [Route("v1")]
    [ApiExplorerSettings(GroupName = "ApiHost.v1")]
    public class V1_ApiHostController : ProductControllerBase
    {
        private readonly IOcelotApiSerivce _ocelotApiSerivce;
        private readonly INacosConfigClient _nacosConfigClient;
        private readonly JObject _appSettingsDto;
        private readonly OcelotNacosConfig _ocelotNacosConfig;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="serviceProvider"></param>
        public V1_ApiHostController(IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _ocelotApiSerivce = serviceProvider.GetService<IOcelotApiSerivce>();
            _nacosConfigClient = serviceProvider.GetService<INacosConfigClient>();
            _appSettingsDto = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");

            _ocelotNacosConfig = new OcelotNacosConfig()
            {
                Host = _appSettingsDto["Nacos"]?["ServerAddresses"]?[0].ToString(),
                DataId = _appSettingsDto["NacosOcelotData"]?["DataId"]?.ToString(),
                Group = _appSettingsDto["NacosOcelotData"]?["Group"]?.ToString(),
                Tenant = _appSettingsDto["Nacos"]?["Namespace"]?.ToString(),
                Type = "json"
            };
        }

        /// <summary>
        /// 获取授权的接口信息
        /// </summary>
        /// <param name="queryDto"></param>
        /// <returns></returns>
        [HttpPost("interfaces")]
        public List<InterfaceInfoDto> GetThirdInterfaces([FromBody] InterfaceInfoQueryDto queryDto)
        {
            queryDto.Password = Crypto.AESEncrypt(queryDto.Password, AESConstant.AesKey, AESConstant.AseVectorKey);
            return _ocelotApiSerivce.GetThirdInterfaces(queryDto);
        }

        /// <summary>
        /// 获取授权的接口结构信息
        /// </summary>
        /// <param name="queryDto"></param>
        /// <returns></returns>
        [HttpPost("interface-schema")]
        public dynamic GetThirdInterfaceSchema(InterfaceSchemaQueryDto queryDto)
        {
            queryDto.Password = Crypto.AESEncrypt(queryDto.Password, AESConstant.AesKey, AESConstant.AseVectorKey);
            return _ocelotApiSerivce.GetThirdInterfaceSchema(queryDto);
        }

        /// <summary>
        /// 获取授权的组合接口信息
        /// </summary>
        /// <param name="queryDto"></param>
        /// <returns></returns>
        [HttpPost("interfaces-group")]
        public List<InterfaceInfoDto> GetThirdGroupInterfaces([FromBody] InterfaceInfoQueryDto queryDto)
        {
            queryDto.Password = Crypto.AESEncrypt(queryDto.Password, AESConstant.AesKey, AESConstant.AseVectorKey);
            return _ocelotApiSerivce.GetThirdGroupInterfaces(queryDto);
        }

        /// <summary>
        /// 获取授权的组合接口结构信息
        /// </summary>
        /// <param name="queryDto"></param>
        /// <returns></returns>
        [HttpPost("interface-group-schema")]
        public dynamic GetThirdGroupInterfacesSchema(InterfaceSchemaQueryDto queryDto)
        {
            queryDto.Password = Crypto.AESEncrypt(queryDto.Password, AESConstant.AesKey, AESConstant.AseVectorKey);
            return _ocelotApiSerivce.GetThirdGroupInterfacesSchema(queryDto);
        }

        /// <summary>
        /// 重新加载ocelot配置文件
        /// </summary>        
        /// <param name="routeDtos"></param>
        [HttpPost("reload-config")]
        public void OcelotConfigReload(List<OcelotRouteExtDto> routeDtos)
        {
            var ocelotConfig = OcelotNacosExtensions.GetOcelotConfig(_nacosConfigClient, _appSettingsDto["NacosOcelotData"]);
            FileConfigurationExtDto ocelotFileConfig = JsonConvert.DeserializeObject<FileConfigurationExtDto>(ocelotConfig);
            var routes = ocelotFileConfig.Routes;

            routeDtos.ForEach(x =>
            {
                var curRoute = JsonConvert.DeserializeObject<FileRouteExtDto>(JsonConvert.SerializeObject(x.Route));

                // 授权
                if (x.ApiAction == "Auth")
                {
                    if (routes.Any(r => r.ApiId == x.Route.ApiId))
                    {
                        routes.Remove(routes.Find(r => r.ApiId == x.Route.ApiId));
                        routes.Add(curRoute);
                    }
                }

                // 发布
                if (x.ApiAction == "Publish")
                {
                    if (routes.Any(r => r.ApiId == x.Route.ApiId))
                    {
                        routes.Remove(routes.Find(r => r.ApiId == x.Route.ApiId));
                    }

                    routes.Add(curRoute);
                }

                // 下线
                if (x.ApiAction == "Offline")
                {
                    if (routes.Any(r => r.ApiId == x.Route.ApiId))
                    {
                        routes.Remove(routes.Find(r => r.ApiId == x.Route.ApiId));
                    }
                }
            });

            ocelotFileConfig.Routes = routes;

            OcelotNacosExtensions.PublishOcelotConfig(_ocelotNacosConfig, ocelotFileConfig);
        }
    }
}
