﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Application.Dtos
{
    /// <summary>
    /// 
    /// </summary>
    [Serializable]
    public class InterfaceInfoDto
    {
        /// <summary>
        /// 
        /// </summary>
        public Guid ApiId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ApiName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string RequestMethod { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string RequestPath { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool IsSecurityCert { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// CategoryId
        /// </summary>
        public Guid? CategoryId { get; set; }

        /// <summary>
        /// CategoryCode
        /// </summary>
        public string CategoryCode { get; set; }

        /// <summary>
        /// CategoryName
        /// </summary>
        public string CategoryName { get; set; }
    }
}
