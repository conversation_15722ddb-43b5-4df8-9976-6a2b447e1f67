﻿using Newtonsoft.Json;

namespace Medusa.Service.Ocelot.ApiHost.Core.Dto.Ocelot
{
    /// <summary>
    /// Ocelot授权 dto
    /// </summary>
    public class OcelotAccessDto
    {
        /// <summary>
        /// Token
        /// </summary>
        [JsonProperty("access_token")]
        public string Token { get; set; }

        /// <summary>
        /// 过期时间（秒）
        /// </summary>
        [JsonProperty("expires_in")]
        public int ExpiresIn { get; set; }

        /// <summary>
        /// Token 类型
        /// </summary>
        [JsonProperty("token_type")]
        public string TokenType { get; set; }

        /// <summary>
        /// Scope
        /// </summary>
        public string Scope { get; set; }
    }
}