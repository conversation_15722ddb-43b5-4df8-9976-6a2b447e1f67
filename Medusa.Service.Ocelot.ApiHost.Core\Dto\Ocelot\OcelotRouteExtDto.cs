﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Core.Dtos
{
    /// <summary>
    /// Ocelot Route Ext
    /// </summary>
    public class OcelotRouteExtDto
    {
        /// <summary>
        /// API Ocelot Action
        /// </summary>
        public string ApiAction { get; set; }

        /// <summary>
        /// 服务类型
        /// </summary>
        public string OcelotServiceType { get; set; }

        /// <summary>
        /// Route
        /// </summary>
        public OcelotRouteDto Route { get; set; }
    }
}
