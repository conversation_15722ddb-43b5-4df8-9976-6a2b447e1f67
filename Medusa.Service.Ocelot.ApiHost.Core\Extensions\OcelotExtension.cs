﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Ocelot.Configuration.File;

namespace Medusa.Service.Ocelot.ApiHost.Core.Extensions
{
    /// <summary>
    /// Ocelot扩展
    /// </summary>
    public class OcelotExtension
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ocelotConfigFolder">ocelot文件目录地址</param>
        /// <returns>Ocelot Config Json</returns>
        public static string GetMergeOcelotConfig(string ocelotConfigFolder)
        {
            var ocelotConfigs = new List<string>() 
            {
                "ocelot.mock.json",
                "ocelot.http.json",
                "ocelot.web_service.json",
                "ocelot.database.json",
                "ocelot.data_trans.json",
                "ocelot.api_group.json"
            };

            var files = new DirectoryInfo(ocelotConfigFolder).EnumerateFiles().ToList();
            files = files.Where(r => ocelotConfigs.Contains(r.Name)).ToList();

            var fileConfiguration = new FileConfiguration();
            foreach (var file in files)
            {
                var lines = System.IO.File.ReadAllText(file.FullName);

                var config = JsonConvert.DeserializeObject<FileConfiguration>(lines);

                fileConfiguration.Aggregates.AddRange(config.Aggregates);
                fileConfiguration.Routes.AddRange(config.Routes);
            }

            return JsonConvert.SerializeObject(fileConfiguration);
        }
    }
}
