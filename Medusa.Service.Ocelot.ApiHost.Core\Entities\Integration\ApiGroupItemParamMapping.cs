using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration
{
    /// <summary>
    /// ApiGroupItemParamMapping
    /// </summary>
    [EntityTable("ApiGroupItemParamMapping", "Api组合项参数映射")]
    public class ApiGroupItemParamMapping
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ParamId { get; set; }

        /// <summary>
        /// 参数名称
        /// </summary>
        [EntityColumn(ColumnDescription = "参数名称", ColumnDataType = "varchar(50)")]
        public string ParamName { get; set; }

        /// <summary>
        /// 参数类型
        /// </summary>
        [EntityColumn(ColumnDescription = "参数类型", ColumnDataType = "varchar(50)")]
        public string DataType { get; set; }

        /// <summary>
        /// 是否必须
        /// </summary>
        [EntityColumn(ColumnDescription = "是否必须")]
        public bool? Required { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [EntityColumn(ColumnDescription = "描述", ColumnDataType = "varchar(500)")]
        public string Description { get; set; }

        /// <summary>
        /// 映射类型 ParamSchema ResultSchema FixedValue
        /// </summary>
        [EntityColumn(ColumnDescription = "映射类型 ParamSchema ResultSchema FixedValue", ColumnDataType = "varchar(20)")]
        public string MappingType { get; set; }

        /// <summary>
        /// 如果是类型是ResultSchema则保存对应的ApiGroupItemId
        /// </summary>
        [EntityColumn(ColumnDescription = "如果是类型是ResultSchema则保存对应的ApiGroupItemId", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ItemId { get; set; }

        /// <summary>
        /// 映射的字段Id
        /// </summary>
        [EntityColumn(ColumnDescription = "映射的字段Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? MappingSchemaId { get; set; }

        /// <summary>
        /// 映射的字段名称
        /// </summary>
        [EntityColumn(ColumnDescription = "映射的字段名称", ColumnDataType = "varchar(50)")]
        public string MappingSchemaName { get; set; }
        
        /// <summary>
        /// 映射的字段数据类型
        /// </summary>
        [EntityColumn(ColumnDescription = "映射的字段数据类型", ColumnDataType = "varchar(20)")]
        public string MappingDataType { get; set; }

        /// <summary>
        /// 参数值如果是类型是FixedValue则存固定值，否则为NULL
        /// </summary>
        [EntityColumn(ColumnDescription = "参数值如果是类型是FixedValue则存固定值", ColumnDataType = "varchar(255)")]
        public string ParamValue { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [EntityColumn(ColumnDescription = "排序")]
        public int? OrderIndex { get; set; }

        /// <summary>
        /// Api组合项主键Id
        /// </summary>
        [EntityColumn(ColumnDescription = "Api组合项主键Id", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = false)]
        public Guid? ApiGroupItemId { get; set; }

        /// <summary>
        /// Api组合主键Id
        /// </summary>
        [EntityColumn(ColumnDescription = "Api组合主键Id", ColumnDataType = "uniqueidentifier,varchar(36)", IsNullable = false)]
        public Guid? ApiGroupId { get; set; }
    }
}
