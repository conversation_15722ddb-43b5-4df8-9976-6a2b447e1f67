﻿using Medusa.Service.Ocelot.ApiHost.Application.Dtos;
using Medusa.Service.Ocelot.ApiHost.Application.OcelotApi;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Xml;

namespace Medusa.Service.Ocelot.ApiHost.Entrance.Extensions
{
    /// <summary>
    /// Web Service 代理
    /// </summary>
    public class OcelotWebDistributor : DelegatingHandler
    {
        private readonly IOcelotApiSerivce _ocelotApiSerivce;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="ocelotApiSerivce"></param>
        public OcelotWebDistributor(IOcelotApiSerivce ocelotApiSerivce)
        {
            _ocelotApiSerivce = ocelotApiSerivce;
        }

        /// <summary>
        /// 发送下游
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            try
            {
                var apiToken = request.Headers.GetValues("api-token").ToList().FirstOrDefault();
                var requestModule = request.Headers.GetValues("request-module").ToList().FirstOrDefault();
                OcelotExtension.RemoveOcelotHeaderKey(request.Headers);

                Apis api = _ocelotApiSerivce.GetApis(apiToken);

                // 请求信息处理
                var requestProcessTask = _ocelotApiSerivce.HttpRequestMsgProcessAsync(api, request, new Dictionary<string, string>()
                {
                   { "RequestModule", requestModule }
                });

                requestProcessTask.Start();

                // 获取数据结果
                var responseProcessTask = requestProcessTask.ContinueWith(requestMsgTask =>
                {
                    var requestMsg = requestMsgTask.Result;
                    var task = base.SendAsync(requestMsg, cancellationToken).ContinueWith(responseTask =>
                    {
                        var response = responseTask.Result;
                        if (!response.IsSuccessStatusCode)
                        {
                            throw new Exception(response.Content.ReadAsStringAsync().Result);
                        }

                        var taskResult = response.Content.ReadAsStringAsync();

                        return taskResult.Result;
                    }, cancellationToken);
                    return task.Result;
                }, cancellationToken);

                // 数据结果信息处理
                return responseProcessTask.ContinueWith(processTask =>
                {
                    var responseBody = processTask.Result;
                    XmlDocument doc = new XmlDocument();
                    doc.LoadXml(responseBody);
                    responseBody = doc.InnerText;

                    var finalResult = _ocelotApiSerivce.ResponseMapping(responseBody, api);
                    return OcelotExtension.OcelotResponse(finalResult);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "OcelotWebDistributor");
                return OcelotExtension.ResponseTaskAsync(new OcelotResponseDto() { ErrorCode = ((int)System.Net.HttpStatusCode.InternalServerError).ToString(), ErrorMessage = ex.Message });
            }
        }
    }
}
