{"Routes": [{"DownstreamPathTemplate": "/api-group", "UpstreamPathTemplate": "/user-group", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Post", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"api-token": "zI7/UNwYq2mveXTVpNlUQJQDIgOKuS28oALYS+9iToYsYd6K0pR1F9qMKO0fcYY+e6hXXhoO7kobfH9M24gtag==", "request-module": "Transparent", "api-category": "internal"}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": "http", "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "127.0.0.1", "Port": 80}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotApiGroupsDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}, {"DownstreamPathTemplate": "/Ocelot/Db", "UpstreamPathTemplate": "/pcc/users", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Post", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"api-token": "HiDHaX1X9eklsBKcdiAhRGwTQANTArFeh+0PaTl1vrCmZnnTZJ+Y4qt1pEZvdjhVoMQJQknND1bdWkP8uMJaiw=="}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": null, "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["Identity.TestApp.Scope", "AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "127.0.0.1", "Port": 0}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotDatabaseDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}, {"DownstreamPathTemplate": "/Ocelot/Db", "UpstreamPathTemplate": "/pcc/add-users", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Post", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"api-token": "ztPI6rijrO2WGlrVVVplRgpwfLqNOjiufiWZcFTbx3wtLcIbIdzudZM56ppR2Q5N2+B31sKhz221JFQ2UshaYg=="}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": null, "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "127.0.0.1", "Port": 0}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotDatabaseDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}, {"DownstreamPathTemplate": "/mdm-orgs", "UpstreamPathTemplate": "/ExRece/ABEF4737F84E46F09632A5D6C7A90B29", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Post", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"receiving-mode": "Active"}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": "http", "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "*************", "Port": 10064}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotDataTransDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}, {"DownstreamPathTemplate": "/mdm-users", "UpstreamPathTemplate": "/ExRece/9C5B57225DCE4715A09E2D5A9F0EA4A2", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Post", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"receiving-mode": "Active"}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": "http", "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "*************", "Port": 10064}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotDataTransDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}, {"DownstreamPathTemplate": "/green-town/master-data/orgs", "UpstreamPathTemplate": "/ExRece/AEF8EA00E8C24AEBB0AA309160A0892D", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Post", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"receiving-mode": "Active"}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": "http", "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "*************", "Port": 10064}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotDataTransDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}, {"DownstreamPathTemplate": "/Commodity/commodities", "UpstreamPathTemplate": "/pcc/commodity", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Post", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"api-token": "qTCs8fNXsH6Cw9PDWdt1nyzO87ctfX7hu9y9pZLvbsvYjcdZFmM08Gg34rbubGcFJOBmqgoG5yejYZ5AuibKRg==", "request-module": "Mapping", "api-category": "external"}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": "http", "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["Identity.PCC_America.Scope", "Identity.bpm.Scope", "AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "*************", "Port": 5004}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotHttpsDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}, {"DownstreamPathTemplate": "/platform/v1/manage/users/{UserId}", "UpstreamPathTemplate": "/Platform/User", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Get", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"api-token": "L9OZ/zSY8PnrF1jyBrI3iDYttRi/8TYcfKrt4E+xYRbXKy8jb4fRswxsSOXD4v4Caa2LIvaBGKnX2s+2727BSA==", "request-module": "Mapping", "api-category": "internal"}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": "http", "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["Identity.bpm.Scope", "AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "127.0.0.1", "Port": 80}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotHttpsDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}, {"DownstreamPathTemplate": "/api/inner/datapub/rest/api/v1/hcm/userForBPM", "UpstreamPathTemplate": "/mdm-users", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Post", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"api-token": "GGA3LEPfCgWwa1/OcGyUNmLF0BCfA+6L8JffK2MTX2urfoOK76ZYJBv2mapaElJH/iacZm5iFtmjU2XMC5BMSg==", "request-module": "Mapping", "api-category": "external"}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": "http", "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["Identity.bpm.Scope", "AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "*************", "Port": 80}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotHttpsDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}, {"DownstreamPathTemplate": "/api/inner/datapub/rest/api/v1/hcm/orgForBPM", "UpstreamPathTemplate": "/mdm-orgs", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Post", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"api-token": "vZN5Mg610V3nnmDoQpcXAsQuAW8747hsT1QF++tvaBXwm/4G3jsvUKxCfnxrvnHpDqvtYGKfikWrETym+g9aWw==", "request-module": "Mapping", "api-category": "external"}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": "http", "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["Identity.bpm.Scope", "AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "*************", "Port": 80}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotHttpsDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}, {"DownstreamPathTemplate": "/engine/v1/test/logs", "UpstreamPathTemplate": "/test/logs", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Post", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"api-token": "8TkYpZT4/LG0Ejl+k70hi7RpSMgbBlQ3KL8widSvYgv77vGFgBr9n0X8vWRL9dIa02YKpk8vVDvA4E1J8rRILw==", "request-module": "Mapping", "api-category": "internal"}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": "http", "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["Identity.bpm.Scope", "AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "127.0.0.1", "Port": 80}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotHttpsDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}, {"DownstreamPathTemplate": "/engine/v1/test/logs", "UpstreamPathTemplate": "/v1/engine", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Post", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"api-token": "dWQQ0chKvhUTBEdzB23+kCJ8psMxf69P84ESng6XV0W7WVFKNyytqMi2j6Ryv6PFX/69DDbZ/3IRc8vByXrZmw==", "request-module": "Transparent", "api-category": "internal"}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": "http", "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "127.0.0.1", "Port": 80}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotHttpsDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}, {"DownstreamPathTemplate": "/v1/bpm/manage/users/{id}", "UpstreamPathTemplate": "/userinfo", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Get", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"api-token": "1fZFLVmy6zqVqT6JsG/ceR1Cxb5Qyv4Nux2x9wL3lJ2CsG9XXqBubhD87Sh1E/ByQMWzvqqAUy/lCcbrSSLCkA==", "request-module": "Mapping", "api-category": "external"}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": "http", "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "*************", "Port": 10051}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotHttpsDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}, {"DownstreamPathTemplate": "/api/service/datapub/rest/api/v1/org/queryOrg", "UpstreamPathTemplate": "/green-town/master-data/orgs", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Get", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"api-token": "SISSlxbM6UsW/I39k8+N5xkh0M2dXUFMO3Pa1Og0so+a5V9yTVWOWS+iMQstB9xnynfbPEj9faDsp2l+5A9fTQ==", "request-module": "Mapping", "api-category": "external"}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": "https", "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "mdm.gtcloud.cn", "Port": 443}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotHttpsDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}, {"DownstreamPathTemplate": "/modeling/v1/business-objects/{id}/data", "UpstreamPathTemplate": "/bpm-master/users", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Get", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"api-token": "QSfiHsWaoNf89P5pgQfuJNxMe4lMY1/Nt7anb1lyGlRbbXxfOgQagOMDCT29RYf/ipmdDEWt3SAEWpKpfs0znw==", "request-module": "Mapping", "api-category": "internal"}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": "http", "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "127.0.0.1", "Port": 80}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotHttpsDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}, {"DownstreamPathTemplate": "/Common_Service.asmx/GetAllApprovalHistory", "UpstreamPathTemplate": "/Pcc/Process-Record", "UpstreamHttpMethod": ["Post"], "DownstreamHttpMethod": "Post", "AddHeadersToRequest": {}, "UpstreamHeaderTransform": {"api-token": "EduhhyzQG49YAHTCgzd6akvWYxzWXASu7XjAZgXBCcza9pN9D4DPwNqqIHIEN19+KUmdvVdOcdgpRjlToVA+8Q==", "request-module": "Mapping", "down-securitycert-model": "None", "down-securitycert-data": null}, "DownstreamHeaderTransform": {}, "AddClaimsToRequest": {}, "RouteClaimsRequirement": {}, "AddQueriesToRequest": {}, "ChangeDownstreamPathTemplate": {}, "RequestIdKey": null, "FileCacheOptions": {"TtlSeconds": 0, "Region": null}, "RouteIsCaseSensitive": false, "ServiceName": null, "ServiceNamespace": null, "DownstreamScheme": "http", "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "RateLimitOptions": {"ClientWhitelist": [], "EnableRateLimiting": false, "Period": null, "PeriodTimespan": 0.0, "Limit": 0}, "AuthenticationOptions": {"AuthenticationProviderKey": "ApiIds4", "AllowedScopes": ["AdminScope"]}, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHostAndPorts": [{"Host": "*************", "Port": 93}], "UpstreamHost": null, "Key": null, "DelegatingHandlers": ["OcelotWebDistributor"], "Priority": 1, "Timeout": 0, "DangerousAcceptAnyServerCertificateValidator": false, "SecurityOptions": {"IPAllowedList": [], "IPBlockedList": []}, "DownstreamHttpVersion": null}], "DynamicRoutes": [], "Aggregates": [], "GlobalConfiguration": {"RequestIdKey": null, "ServiceDiscoveryProvider": {"Scheme": null, "Host": null, "Port": 0, "Type": "Nacos", "Token": null, "ConfigurationKey": null, "PollingInterval": 0, "Namespace": null}, "RateLimitOptions": {"ClientIdHeader": "ClientId", "QuotaExceededMessage": null, "RateLimitCounterPrefix": "ocelot", "DisableRateLimitHeaders": false, "HttpStatusCode": 429}, "QoSOptions": {"ExceptionsAllowedBeforeBreaking": 0, "DurationOfBreak": 0, "TimeoutValue": 0}, "BaseUrl": null, "LoadBalancerOptions": {"Type": null, "Key": null, "Expiry": 0}, "DownstreamScheme": null, "HttpHandlerOptions": {"AllowAutoRedirect": false, "UseCookieContainer": false, "UseTracing": false, "UseProxy": true, "MaxConnectionsPerServer": **********}, "DownstreamHttpVersion": null}}