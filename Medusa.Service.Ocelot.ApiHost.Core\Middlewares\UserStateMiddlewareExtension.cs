using Microsoft.AspNetCore.Builder;

namespace Medusa.Service.Ocelot.ApiHost.Core.Middlewares
{
    /// <summary>
    /// 获取当前用户的扩展
    /// </summary>
    public static class UserStateMiddlewareExtension
    {
        /// <summary>
        /// 获取当前用户的扩展
        /// </summary>
        /// <param name="builder">构造</param>
        /// <returns>应用构造器</returns>
        public static IApplicationBuilder UseUserState(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<UserStateMiddleware>();
        }
    }
}
