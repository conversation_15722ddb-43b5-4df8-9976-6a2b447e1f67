﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Application.Dtos
{
    /// <summary>
    /// SQL Config Dto 
    /// </summary>
    public class SearchDataDto
    {
        /// <summary>
        /// Host
        /// </summary>
        public string DbHost { get; set; }

        /// <summary>
        /// 数据库
        /// </summary>
        public string DbName { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// SQL语句
        /// </summary>
        public string Sql { get; set; }

        /// <summary>
        /// 数据库类型
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 是否分页
        /// </summary>
        public bool Paging { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        public string OrderBy { get; set; }
    }
}
