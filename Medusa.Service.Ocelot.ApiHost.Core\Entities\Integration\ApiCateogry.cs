using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration
{
    /// <summary>
    /// 分类表
    /// </summary>
    [EntityTable("ApiCategory", "分类表")]
    public class ApiCateogry
    {
        /// <summary>
        /// 分类ID
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "分类ID", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? CategoryId { get; set; }

        /// <summary>
        /// 分类编码
        /// </summary>
        [EntityColumn(ColumnDescription = "分类编码")]
        public string CategoryCode { get; set; }

        /// <summary>
        /// 类别
        /// </summary>
        [EntityColumn(ColumnDescription = "类别")]
        public string CategoryType { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        [EntityColumn(ColumnDescription = "分类名称")]
        public string CategoryName { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [EntityColumn(ColumnDescription = "创建人")]
        public string Creator { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        [EntityColumn(ColumnDescription = "创建人Id")]
        public string CreatorId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [EntityColumn(ColumnDescription = "最新登录时间", ColumnDataType = "datetime(3),datetime")]
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        [EntityColumn(ColumnDescription = "修改人")]
        public string Modifier { get; set; }

        /// <summary>
        /// 修改人Id
        /// </summary>
        [EntityColumn(ColumnDescription = "修改人Id")]
        public string ModifierId { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [EntityColumn(ColumnDescription = "修改时间", ColumnDataType = "datetime(3),datetime")]
        public DateTime? ModifyDate { get; set; }

        /// <summary>
        /// 状态 1 有效 0 无效
        /// </summary>
        [EntityColumn(ColumnDescription = "状态 1 有效 0 无效")]
        public int? Status { get; set; }
    }
}
