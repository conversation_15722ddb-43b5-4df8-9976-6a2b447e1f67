﻿using Medusa.Service.Ocelot.ApiHost.Application.Dtos;
using Medusa.Service.Ocelot.ApiHost.Application.OcelotApi;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration;
using MT.Enterprise.SDK.ApiRequest;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using Microsoft.Extensions.DependencyInjection;
using System.Threading;
using System.Threading.Tasks;
using MT.Enterprise.Core.GlobalExceptions;

namespace Medusa.Service.Ocelot.ApiHost.Entrance.Extensions
{
    /// <summary>
    /// Http/Https 代理
    /// </summary>
    public class OcelotHttpsDistributor : DelegatingHandler
    {
        private readonly IOcelotApiSerivce _ocelotApiSerivce;
        private readonly IRequestConnector _requestConnector;

        /// <summary>
        /// OcelotHttpsDistributor
        /// </summary>
        /// <param name="serviceProvider"></param>
        public OcelotHttpsDistributor(IServiceProvider serviceProvider)
        {
            _ocelotApiSerivce = serviceProvider.GetService<IOcelotApiSerivce>(); ;
            _requestConnector = serviceProvider.GetService<IRequestConnector>();
        }

        /// <summary>
        /// 重构Send
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            try
            {
                var apiToken = request.Headers.GetValues("api-token").ToList().FirstOrDefault();
                var apiCategory = request.Headers.GetValues("api-category").ToList().FirstOrDefault();
                var requestModule = request.Headers.GetValues("request-module").ToList().FirstOrDefault();
                OcelotExtension.RemoveOcelotHeaderKey(request.Headers);

                Apis api = _ocelotApiSerivce.GetApis(apiToken);

                // 请求信息处理
                var requestProcessTask = _ocelotApiSerivce.HttpRequestMsgProcessAsync(api, request, new Dictionary<string, string>()
                {
                   { "RequestModule", requestModule },
                   { "DownstreamAuth", true.ToString() }
                });

                requestProcessTask.Start();

                // 数据结果信息处理
                var responseProcessTask = requestProcessTask.ContinueWith(requestMsg =>
                {
                    if (apiCategory == "internal")
                    {
                        return SendInternalApiAsync(requestMsg.Result).Result;
                    }
                    else
                    {
                        var request = requestMsg.Result;
                        return base.SendAsync(request, cancellationToken).Result;
                    }

                }, cancellationToken);

                return responseProcessTask.ContinueWith(responseTask =>
                {
                    var response = responseTask.Result;
                    var resultBody = response.Content.ReadAsStringAsync().Result;

                    if (!response.IsSuccessStatusCode)
                    {
                        return OcelotExtension.OcelotResponse(new OcelotResponseDto() { ErrorCode = ((int)System.Net.HttpStatusCode.InternalServerError).ToString(), ErrorMessage = resultBody });
                    }
                    else
                    {
                        _ocelotApiSerivce.ExecCustomSuccess(api, resultBody);
                    }

                    var finalResult = _ocelotApiSerivce.ResponseMapping(resultBody, api);

                    return OcelotExtension.OcelotResponse(finalResult);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "OcelotHttpsDistributor");
                return OcelotExtension.ResponseTaskAsync(new OcelotResponseDto() { ErrorCode = ((int)System.Net.HttpStatusCode.InternalServerError).ToString(), ErrorMessage = ex.Message });
            }
        }

        private async Task<HttpResponseMessage> SendInternalApiAsync(HttpRequestMessage request)
        {
            string result = string.Empty;
            if (request.Headers.Contains("current_user"))
            {
                _requestConnector.UserState = request.Headers.GetValues("current_user").FirstOrDefault();
            }
            else
            {
                _requestConnector.UserState = string.Empty;
            }

            switch (request.Method.Method)
            {
                case "Get":
                    result = await _requestConnector.GetAsync<string>(request.RequestUri.PathAndQuery, typeof(StatusBaseException));
                    break;
                case "Post":
                    var postBody = Newtonsoft.Json.Linq.JToken.Parse (request.Content.ReadAsStringAsync().Result);
                    result = await _requestConnector.PostAsync<string>(request.RequestUri.PathAndQuery, postBody, typeof(StatusBaseException));
                    break;
                case "Put":
                    result = await _requestConnector.PutAsync<string>(request.RequestUri.PathAndQuery, request.Content.ReadAsStringAsync().Result, typeof(StatusBaseException));
                    break;
                case "Delete":
                    result = await _requestConnector.DeleteAsync<string>(request.RequestUri.PathAndQuery, typeof(StatusBaseException));
                    break;
            }

            var responseMsg = new HttpResponseMessage(System.Net.HttpStatusCode.OK);
            responseMsg.Content = new StringContent(result);

            return await Task.FromResult(responseMsg);
        }
    }
}
