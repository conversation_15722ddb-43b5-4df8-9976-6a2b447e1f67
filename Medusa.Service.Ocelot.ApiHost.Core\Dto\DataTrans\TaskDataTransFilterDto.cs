﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Core.Dtos
{
    /// <summary>
    /// 
    /// </summary>
    public class TaskDataTransFilterDto
    {
        /// <summary>
        /// 属性名称
        /// </summary>
        public string PropertyName { get; set; }

        /// <summary>
        /// 属性类型
        /// </summary>
        public string PropertyType { get; set; }

        /// <summary>
        /// 属性描述
        /// </summary>
        public string PropertyDescription { get; set; }

        /// <summary>
        /// 过滤值
        /// </summary>
        public string FilterValue { get; set; }

        /// <summary>
        /// 判断符
        /// </summary>
        public string Judge { get; set; }
    }
}
