{"Routes": [{"DownstreamScheme": "http", "DownstreamPathTemplate": "/api-group", "DownstreamHttpMethod": "Post", "DownstreamHostAndPorts": [{"Host": "127.0.0.1", "Port": 80}], "UpstreamPathTemplate": "/user-group", "UpstreamHttpMethod": ["Post"], "UpstreamHeaderTransform": {"api-token": "zI7/UNwYq2mveXTVpNlUQJQDIgOKuS28oALYS+9iToYsYd6K0pR1F9qMKO0fcYY+e6hXXhoO7kobfH9M24gtag==", "request-module": "Transparent", "api-category": "internal"}, "DelegatingHandlers": ["OcelotApiGroupsDistributor"], "AuthenticationOptions": {"AllowedScopes": ["AdminScope"], "AuthenticationProviderKey": "ApiIds4"}, "ApiId": "44f21bc0-789d-47ee-9b07-2fdeea5144bd"}]}