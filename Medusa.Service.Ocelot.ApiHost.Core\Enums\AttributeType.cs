﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Core.Enums
{
    /// <summary>
    /// 分页属性
    /// </summary>
    public enum AttributeType
    {
        /// <summary>
        /// 页码
        /// </summary>
        [Description("页码")]
        PageIndex,

        /// <summary>
        /// 页数
        /// </summary>
        [Description("页数")]
        PageSize,

        /// <summary>
        /// 日期增量(YYYY-MM-DD)
        /// </summary>
        [Description("日期增量(YYYY-MM-DD)")]
        ShortDateIncrement
    }
}
