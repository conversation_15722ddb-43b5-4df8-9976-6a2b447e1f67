﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Core.Enums
{
    /// <summary>
    /// 结果映射源
    /// </summary>
    public enum MappingDataSource
    {
        /// <summary>
        /// 下游返回结果结构
        /// </summary>
        [Description("下游返回结果结构")]
        ResultSchema,

        /// <summary>
        /// 请求体
        /// </summary>
        [Description("请求体")]
        RequestBody,

        /// <summary>
        /// 固定值
        /// </summary>
        [Description("固定值")]
        FixedValue
    }
}
