using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core;
using MT.Enterprise.Core.ORM;
using Newtonsoft.Json.Linq;
using MT.Enterprise.SDK;
using MT.Enterprise.Utils;
using MT.Enterprise.Utils.Extensions;
using Nacos;
using System.Linq;
using System;
using System.IO;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using MT.Enterprise.Core.I18n;
using Medusa.Service.ThirdProvide.Application;
using Microsoft.OpenApi.Models;
using Medusa.Service.Ocelot.ApiHost.Entrance.Development;
using Medusa.Service.Ocelot.ApiHost.Core;
using System.Globalization;
using Microsoft.AspNetCore.Localization;
using MT.Enterprise.Core.GlobalExceptions;
using Microsoft.Extensions.Hosting;
using Medusa.Service.Ocelot.ApiHost.Core.Middlewares;
using Ocelot.DependencyInjection;
using IdentityServer4.AccessTokenValidation;
using Medusa.Service.Ocelot.ApiHost.Entrance.Extensions;
using Ocelot.Middleware;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using MT.Enterprise.SDK.ApiRequest;
using MongoDB.Driver;
using MT.Enterprise.SDK.Transport;
using MT.Enterprise.SDK.CronJob;
using Ocelot.Provider.Nacos;

namespace Medusa.Service.Ocelot.ApiHost.Entrance
{
    /// <summary>
    /// 
    /// </summary>
    public class Startup
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="configuration"></param>
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        /// 配置
        /// </summary>
        public IConfiguration Configuration { get; }

        /// <summary>
        /// AppConfig Json Object
        /// </summary>
        private JObject AppConfigDto { get; set; }

        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="services"></param>
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers();
            services.AddMemoryCache();

            var nacosConfig = Configuration.GetSection("Nacos");
            services.AddNacos(configure =>
            {
                configure.DefaultTimeOut = nacosConfig["DefaultTimeOut"].ToOurInt();
                configure.ServerAddresses = nacosConfig.GetSection("ServerAddresses").Get<string[]>().ToList();
                configure.Namespace = nacosConfig["Namespace"];
                configure.ListenInterval = nacosConfig["ListenInterval"].ToOurInt();
            });

            AppConfigDto = CreateRegister(services, nacosConfig);

            services.AddControllers().AddNewtonsoftJson(options =>
            {
                // 使用 iso 格式化日期
                options.SerializerSettings.DateFormatString = Clock.GetFormat(Clock.ClockType.DateTime);
                options.SerializerSettings.DateParseHandling = Newtonsoft.Json.DateParseHandling.None;
            });

            services.AddCore(core =>
            {
                core.AddI18n(opt =>
                {
                    opt.Languages = GetI18Languages(services, nacosConfig);
                    opt.DefaultLang = AppConstants.I18nDefaultLang;
                });

                core.AddORM(orm =>
                {
                    foreach (var dbConfig in AppConfigDto["Persistence"]!.Children())
                    {
                        string dbName = ((JProperty)dbConfig).Name;
                        Enum.TryParse(dbConfig.First()["DbType"]?.ToString(), true, out ConnectionDbType dbType);
                        orm.AddDbContext(opt =>
                        {
                            opt.Name = dbName;
                            opt.ConnectionString = dbConfig.First()["ConnectionString"]?.ToString(); // dbConfig["ConnectionString"];
                            opt.ConnectionDbType = dbType;
                            opt.IsDefault = true;
                        });
                    }
                });
            });

            var dbObj = AppConfigDto["CollectSetting"];
            if (dbObj != null)
            {
                var connectString = dbObj["MongoConnectionString"].ToString();
                services.AddSingleton<IMongoClient>(new MongoClient(connectString));
            }

            // application service 注入
            services.AddApplicationServices(Configuration);

            // 添加 SDK
            services.AddSDK(sdk =>
            {
                // API调用
                sdk.AddApiRequest(opt =>
                {
                    opt.Host = AppConfigDto["ApiRequest"]?["Host"]?.ToString();
                    opt.Db = Convert.ToInt32(AppConfigDto["ApiRequest"]?["DB"]);
                    opt.Password = AppConfigDto["ApiRequest"]?["Password"]?.ToString();
                    opt.Timeout = Convert.ToInt32(AppConfigDto["ApiRequest"]?["Timeout"]);
                    opt.IsHttps = (AppConfigDto["ApiRequest"]?["IsHttps"].Value<bool>()).Value;
                });

                sdk.AddCronJob(opt =>
                {
                    opt.Host = AppConfigDto["CronJob"]?["Host"]?.ToString();
                    opt.Port = Convert.ToInt32(AppConfigDto["CronJob"]?["Port"]);
                    opt.Uri = AppConfigDto["CronJob"]?["Uri"]?.ToString();
                    opt.BasicUserName = AppConfigDto["CronJob"]?["BasicUserName"]?.ToString();
                    opt.BasicPassword = AppConfigDto["CronJob"]?["BasicPassword"]?.ToString();
                    opt.DefaultTimeOut = Convert.ToInt32(AppConfigDto["CronJob"]?["DefaultTimeOut"]);
                });

                // Rabbit MQ
                sdk.AddTransport(opt =>
                {
                    opt.RabbitMQHostName = AppConfigDto["RabbitMQ"]?["HostName"]?.ToString();
                    opt.RabbitMQUserName = AppConfigDto["RabbitMQ"]?["UserName"]?.ToString();
                    opt.RabbitMQPassword = AppConfigDto["RabbitMQ"]?["Password"]?.ToString();
                    opt.RabbitMQPort = int.Parse(AppConfigDto["RabbitMQ"]?["Port"]?.ToString());
                });
            });

            services.AddSwaggerGen(options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "服务列表", Version = "v1" });

                // 如果没标注版本，或标注的版本包含当前版本，则显示该API
                options.DocInclusionPredicate((docname, description) => description.GroupName?.Split(".").Last() == docname);

                // 添加文档
                options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "Medusa.Service.Ocelot.ApiHost.Entrance.xml"));
                options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "Medusa.Service.Ocelot.ApiHost.Application.xml"));
                options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "Medusa.Service.Ocelot.ApiHost.Core.xml"));

                // Api 分组和展示
                options.TagActionsBy(api => { return new List<string> { api.GroupName }; });
                options.CustomOperationIds(api => $"{api.ActionDescriptor.RouteValues["controller"]}.{api.ActionDescriptor.RouteValues["action"]}");
                options.IgnoreObsoleteActions();
                options.IgnoreObsoleteProperties();

                // 添加默认头信息
                options.OperationFilter<CustomHeaderFilter>();

                // 为附件上传的接口添加上传控件
                options.OperationFilter<SwaggerFileUploadFilter>();
            });

            // 添加跨域配置
            services.AddCors(options =>
            {
                options.AddPolicy("any", builder =>
                {
                    builder.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader();
                });
            });

            // IdentityService配置
            var identityBuilder = services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme);
            if (AppConfigDto["Ids4Setting"] != null)
            {
                identityBuilder.AddIdentityServerAuthentication("ApiIds4", options =>
                {
                    options.Authority = $"http://{AppConfigDto["Ids4Setting"]?["IP"]?.ToString()}:{AppConfigDto["Ids4Setting"]?["Port"]?.ToString()}";
                    options.RequireHttpsMetadata = false;
                    options.SupportedTokens = SupportedTokens.Both;
                });
            }

            // Ocelot
            var ocelotConiguration = services.AddOcelotNacosConfig(AppConfigDto["NacosOcelotData"]);
            services.AddOcelot(ocelotConiguration)
                .AddDelegatingHandler<OcelotMockDistributor>()
                .AddDelegatingHandler<OcelotHttpsDistributor>()
                .AddDelegatingHandler<OcelotDatabaseDistributor>()
                .AddDelegatingHandler<OcelotWebDistributor>()
                .AddDelegatingHandler<OcelotDataTransDistributor>()
                .AddDelegatingHandler<OcelotApiGroupsDistributor>()
                .AddNacosDiscovery();
        }

        /// <summary>
        /// 应用
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        /// <param name="memoryCache"></param>
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IMemoryCache memoryCache)
        {
            var supportedCultures = new[]
            {
                new CultureInfo("zh-CN"),
                new CultureInfo("en-US"),
            };
            app.UseRequestLocalization(new RequestLocalizationOptions
            {
                DefaultRequestCulture = new RequestCulture(AppConstants.I18nDefaultLang),
                SupportedCultures = supportedCultures,
                SupportedUICultures = supportedCultures,
            });

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseErrorHandling(true);
            }

            //配置Cors
            app.UseCors("any");

            var entryOptions = new MemoryCacheEntryOptions().SetPriority(CacheItemPriority.NeverRemove);
            memoryCache.Set("AppSettings", AppConfigDto, entryOptions);

            app.UseUserState();
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();//授权

            app.UseSwagger(options => { options.RouteTemplate = "swagger/{documentName}/swagger.json"; });
            app.UseSwaggerUI(options =>
            {
                options.SwaggerEndpoint("/swagger/v1/swagger.json", "Api List - v1");
                options.DisplayOperationId();
                options.DisplayRequestDuration();
                options.EnableDeepLinking();
                options.EnableFilter();
            });

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });

            app.UseOcelot().Wait();

            app.UseListenerOcelotConfig(AppConfigDto["NacosOcelotData"].ToString());
        }

        /// <summary>
        /// CreateRegister
        /// </summary>
        /// <param name="services">services</param>
        /// <param name="section">section</param>
        /// <returns></returns>
        protected JObject CreateRegister(IServiceCollection services, IConfigurationSection section)
        {
            var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            if (!string.IsNullOrEmpty(environmentName) && (environmentName == "Local" || environmentName == "Development"))
            {
                string config = string.Empty;
                if (environmentName == "Development")
                {
                    config = File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), "appsettings.Development.json"));
                }
                else if (environmentName == "Local")
                {
                    config = File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), "appsettings.Local.json"));
                }

                return JObject.Parse(config);
            }
            else
            {
                var serviceProvider = services.BuildServiceProvider();
                var configClient = serviceProvider.GetService<INacosConfigClient>();

                var res = configClient.GetConfigAsync(new GetConfigRequest
                {
                    DataId = section["DataId"],
                    Group = section["GroupId"],
                }).Result;

                if (!string.IsNullOrEmpty(res))
                {
                    var appSettings = JObject.Parse(res);
                    return appSettings;
                }

                throw new Exception("Not Found Nacos!");
            }
        }

        /// <summary>
        /// 获取I18多语言文件
        /// </summary>
        /// <param name="services">services</param>
        /// <param name="section">section</param>
        /// <returns>Dictionary</returns>
        private Dictionary<string, string> GetI18Languages(IServiceCollection services, IConfigurationSection section)
        {
            var result = new Dictionary<string, string>();

            var serviceProvider = services.BuildServiceProvider();
            var configClient = serviceProvider.GetService<INacosConfigClient>();

            var languageIds = section.GetSection("LanguageIds").Get<string[]>().ToList();

            foreach (var languageId in languageIds)
            {
                var matched = Regex.Matches(languageId, @"([A-Za-z0-9-_]+)").ToList();
                var res = configClient.GetConfigAsync(new GetConfigRequest
                {
                    DataId = languageId,
                    Group = section["LanguageGroupId"],
                }).Result;
                result.Add(matched[0].ToString(), res);
            }

            return result;
        }
    }
}
