using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration
{
    /// <summary>
    /// ApiGroupItems
    /// </summary>
    [EntityTable("ApiGroupItems", "Api组合子项")]
    public class ApiGroupItem
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ApiGroupItemId { get; set; }

        /// <summary>
        /// Api组合主键Id
        /// </summary>
        [EntityColumn(ColumnDescription = "Api组合主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ApiGroupId { get; set; }

        /// <summary>
        /// Api主键Id
        /// </summary>
        [EntityColumn(ColumnDescription = "Api主键Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ApiBaseId { get; set; }

        /// <summary>
        /// 入参请求模式
        /// </summary>
        [EntityColumn(ColumnDescription = "入参请求模式", ColumnDataType = "varchar(20)")]
        public string RequestParamModule { get; set; }

        /// <summary>
        /// 调用顺序
        /// </summary>
        [EntityColumn(ColumnDescription = "调用顺序")]
        public int? RequestOrder { get; set; }
    }
}
