stages:
 - lint_and_build
 - deploy
 - release
 
before_script:
 - if [[ -z $PROJECT_NAME ]]; then echo 'Missing Secret Variable -> $PROJECT_NAME' && exit 10; fi
 - if [[ -z $PM_PROJECT_NAME ]]; then echo 'Missing Secret Variable -> $PM_PROJECT_NAME' && exit 10; fi
 - SOURCE_PUBLIC=https://api.nuget.org/v3/index.json
 - SOURCE_PRIVATE=http://*************:15840/nuget
 - HARBOR=*************:8858/matrix
 
lint_and_build_task:
 stage: lint_and_build
 except:
  - develop
  - master
  - tags
 script:
  - sed -i -e 's/Warning/Error/g' csharp.ruleset
  - dotnet restore -s $SOURCE_PUBLIC -s $SOURCE_PRIVATE
  - dotnet build ./Medusa.Service.Ocelot.ApiHost.Entrance/Medusa.Service.Ocelot.ApiHost.Entrance.csproj --no-restore

dev_deploy_task:
 stage: deploy
 only: 
  - develop
 script:
  - dotnet restore -s $SOURCE_PUBLIC -s $SOURCE_PRIVATE
  - dotnet publish ./Medusa.Service.Ocelot.ApiHost.Entrance/Medusa.Service.Ocelot.ApiHost.Entrance.csproj --no-restore -o /tmp/publish/$PROJECT_NAME/tmp
  - tar -cvzf /tmp/$PROJECT_NAME.tgz -C /tmp/publish/$PROJECT_NAME tmp
  - deploy_copy /tmp/$PROJECT_NAME.tgz /var/www/low-code-platform/integration.center/$PROJECT_NAME
  - deploy_run "cd /var/www/low-code-platform/integration.center/$PROJECT_NAME && tar -xvzf $PROJECT_NAME.tgz"
  - deploy_run "cd /var/www/low-code-platform/integration.center/$PROJECT_NAME && rm $PROJECT_NAME.tgz"
  - deploy_run "mv /var/www/low-code-platform/integration.center/$PROJECT_NAME/src/Ocelot /var/www/low-code-platform/integration.center/$PROJECT_NAME"
  - deploy_run "rm -rf /var/www/low-code-platform/integration.center/$PROJECT_NAME/src"
  - deploy_run "mv /var/www/low-code-platform/integration.center/$PROJECT_NAME/tmp /var/www/low-code-platform/integration.center/$PROJECT_NAME/src"
  - deploy_run "rm -rf /var/www/low-code-platform/integration.center/$PROJECT_NAME/src/Ocelot"
  - deploy_run "mv /var/www/low-code-platform/integration.center/$PROJECT_NAME/Ocelot /var/www/low-code-platform/integration.center/$PROJECT_NAME/src"
  - deploy_run "pm2 restart $PM_PROJECT_NAME"
  
release_task:
 stage: release
 only:
  - tags
 script:
  - deploy_run "cd /var/www/low-code-platform/integration.center/$PROJECT_NAME/src && docker build -t $PROJECT_NAME:$CI_COMMIT_TAG  ."
  - deploy_run "docker tag $PROJECT_NAME:$CI_COMMIT_TAG  $HARBOR/$PROJECT_NAME:$CI_COMMIT_TAG"
  - deploy_run "docker push $HARBOR/$PROJECT_NAME:$CI_COMMIT_TAG"
  - deploy_run "docker rmi $HARBOR/$PROJECT_NAME:$CI_COMMIT_TAG && docker rmi $PROJECT_NAME:$CI_COMMIT_TAG"

