﻿using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Application.Extensions
{
    /// <summary>
    /// 
    /// </summary>
    public class CryptoExtension
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="target"></param>
        /// <returns></returns>
        public static string Md5(string target)
        {
            try
            {
                string entry = string.Empty;
                MD5 md5 = MD5.Create(); 
                                        
                byte[] s = md5.ComputeHash(Encoding.UTF8.GetBytes(target));
                for (int i = 0; i < s.Length; i++)
                {
                    entry = entry + s[i].ToString("x2");
                }

                return entry;
            }
            catch
            {
                return string.Empty;
            }
        }
    }
}
