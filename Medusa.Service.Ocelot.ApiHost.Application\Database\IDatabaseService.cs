﻿using System;
using System.Collections.Generic;
using System.Data;
using Medusa.Service.Ocelot.ApiHost.Application.Dtos;
using Medusa.Service.Ocelot.ApiHost.Application.Enum;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration;
using SqlSugar;

namespace Medusa.Service.Ocelot.ApiHost.Application.Database
{
    /// <summary>
    /// 数据库接口
    /// </summary>
    public interface IDatabaseService
    {
        /// <summary>
        /// 数据库类型
        /// </summary>
        DatabaseType Type { get; }

        /// <summary>
        /// 执行Sql
        /// </summary>
        /// <param name="dbConf">dbConf</param>
        /// <param name="sql">sql</param>
        /// <param name="parameters"></param>
        /// <param name="outTable"></param>
        void ExceSql(ServicesDatabase dbConf, string sql, List<SugarParameter> parameters, out DataTable outTable);
    }
}
