﻿using Newtonsoft.Json;
using System;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Medusa.Service.Ocelot.ApiHost.Application.OcelotApi;
using Medusa.Service.Ocelot.ApiHost.Application.Dtos;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration;
using Serilog;
using Newtonsoft.Json.Linq;

namespace Medusa.Service.Ocelot.ApiHost.Entrance.Extensions
{
    /// <summary>
    /// 
    /// </summary>
    public class OcelotDatabaseDistributor : DelegatingHandler
    {
        private readonly IOcelotApiSerivce _ocelotApiSerivce;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="thirdProvideSerivce"></param>
        public OcelotDatabaseDistributor(IOcelotApiSerivce thirdProvideSerivce)
        {
            _ocelotApiSerivce = thirdProvideSerivce;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            try
            {
                var apiToken = request.Headers.GetValues("api-token").ToList().FirstOrDefault();
                Apis api = _ocelotApiSerivce.GetApis(apiToken);

                // 获取参数
                var postJsonToken = JsonConvert.DeserializeObject<JToken>(request.Content.ReadAsStringAsync().Result);

                var result = _ocelotApiSerivce.ExecSqlForDb(api, postJsonToken);

                return OcelotExtension.ResponseTaskAsync(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "OcelotDatabaseDistributor");

                return OcelotExtension.ResponseTaskAsync(new OcelotResponseDto() { ErrorCode = ((int)System.Net.HttpStatusCode.InternalServerError).ToString(), ErrorMessage = ex.Message });
            }
        }

        /// <summary>
        /// 
        /// </summary>
        public class PageTable
        {
            /// <summary>
            /// 
            /// </summary>
            public int count { get; set; }

            /// <summary>
            /// 
            /// </summary>
            public System.Data.DataTable item { get; set; }
        }
    }
}
