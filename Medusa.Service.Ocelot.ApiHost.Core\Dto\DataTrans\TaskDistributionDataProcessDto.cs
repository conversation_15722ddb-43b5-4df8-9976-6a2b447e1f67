﻿using System.Collections.Generic;

namespace Medusa.Service.Ocelot.ApiHost.Core.Dtos
{
    /// <summary>
    /// 任务-数据分发数据处理
    /// </summary>
    public class TaskDistributionDataProcessDto
    {
        /// <summary>
        /// 数据过滤
        /// </summary>
        public List<TaskDataTransFilterDto> Filters { get; set; }

        /// <summary>
        /// 是否前置处理
        /// </summary>
        public bool PreProcess { get; set; }

        /// <summary>
        /// 处理方式（前置处理生效）
        /// </summary>
        public string ProcessMethod { get; set; }

        /// <summary>
        /// 处理API（前置处理生效）
        /// </summary>
        public string API { get; set; }

        /// <summary>
        /// 处理代码（前置处理生效）
        /// </summary>
        public string Code { get; set; }
    }
}
