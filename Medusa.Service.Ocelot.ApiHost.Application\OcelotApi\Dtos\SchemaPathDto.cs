﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Application.Dtos
{
    /// <summary>
    /// 
    /// </summary>
    public class SchemaPathDto
    {
        /// <summary>
        /// 
        /// </summary>
        public Guid SchemaId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string SchemaName { get; set; }

        /// <summary>
        /// 路径：
        /// 对象.属性(非对象)
        /// 对象.属性(对象).属性
        /// 对象.属性(集合)
        /// </summary>
        public string SchemaTreePath { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Guid? UpperSchemaId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int? OrderIndex { get; set; }
    }
}
