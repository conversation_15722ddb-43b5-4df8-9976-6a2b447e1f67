﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Core.Dto.Identity
{
    /// <summary>
    /// 
    /// </summary>
    [Serializable]
    public class GatewayIdentityServerConfigDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string IP { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Port { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string IdentityScheme { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<GatewayApiResourceDto> Resources { get; set; }
    }
}
