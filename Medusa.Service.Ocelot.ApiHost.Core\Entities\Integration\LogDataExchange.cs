using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration
{
    /// <summary>
    /// 数据交换日志
    /// </summary>
    [EntityTable("Log_DataExchange", "数据交换日志")]
    public class LogDataExchange
    {
        /// <summary>
        /// LogId
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, IsIdentity = true)]
        public long? LogId { get; set; }

        /// <summary>
        /// 任务号
        /// </summary>
        [EntityColumn(ColumnDescription = "任务号")]
        public string TaskNumber { get; set; }

        /// <summary>
        /// 子任务号
        /// </summary>
        [EntityColumn(ColumnDescription = "子任务号")]
        public string SubTaskNumber { get; set; }

        /// <summary>
        /// 任务阶段
        /// </summary>
        [EntityColumn(ColumnDescription = "任务阶段")]
        public string TaskStage { get; set; }

        /// <summary>
        /// 任务状态
        /// </summary>
        [EntityColumn(ColumnDescription = "任务状态")]
        public int? TaskStatus { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        [EntityColumn(ColumnDescription = "执行时间", ColumnDataType = "datetime(3),datetime")]
        public DateTime? ExecutionTime { get; set; }

        /// <summary>
        /// 模型交换编码
        /// </summary>
        [EntityColumn(ColumnDescription = "模型交换编码")]
        public string ModelCode { get; set; }

        /// <summary>
        /// 模型交换名称
        /// </summary>
        [EntityColumn(ColumnDescription = "模型交换名称")]
        public string ModeName { get; set; }

        /// <summary>
        /// 交换模型Id
        /// </summary>
        [EntityColumn(ColumnDescription = "交换模型Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ExchangeModelId { get; set; }

        /// <summary>
        /// 数据接收器
        /// </summary>
        [EntityColumn(ColumnDescription = "数据接收器")]
        public string Receiver { get; set; }

        /// <summary>
        /// 接收配置Id
        /// </summary>
        [EntityColumn(ColumnDescription = "接收配置Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ReceiveId { get; set; }

        /// <summary>
        /// 分发器
        /// </summary>
        [EntityColumn(ColumnDescription = "分发器")]
        public string Dispenser { get; set; }

        /// <summary>
        /// 分发源Id
        /// </summary>
        [EntityColumn(ColumnDescription = "分发源Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? DistributionId { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [EntityColumn(ColumnDescription = "错误信息")]
        public string ErrMsg { get; set; }

        /// <summary>
        /// 数据信息
        /// </summary>
        [EntityColumn(ColumnDescription = "数据信息")]
        public string DataMsg { get; set; }

        /// <summary>
        /// 失败重试次数
        /// </summary>
        [EntityColumn(ColumnDescription = "失败重试次数")]
        public int? RetryCount { get; set; }
    }
}
