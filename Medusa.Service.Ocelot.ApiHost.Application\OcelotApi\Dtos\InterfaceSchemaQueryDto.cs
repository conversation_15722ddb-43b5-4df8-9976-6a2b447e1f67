﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Application.Dtos
{
    /// <summary>
    /// 
    /// </summary>
    [Serializable]
    public class InterfaceSchemaQueryDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string AppCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Guid ApiId { get; set; }
    }
}
