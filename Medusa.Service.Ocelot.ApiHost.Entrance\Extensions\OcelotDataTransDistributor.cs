﻿using Medusa.Service.Ocelot.ApiHost.Application.Dtos;
using Medusa.Service.Ocelot.ApiHost.Application.OcelotApi;
using Medusa.Service.Ocelot.ApiHost.Core.Dtos;
using Newtonsoft.Json.Linq;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using MongoDB.Driver;
using Microsoft.Extensions.Caching.Memory;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.MongoEntities;
using MT.Enterprise.Utils.Extensions;
using MT.Enterprise.SDK.Transport;
using System.Net.Http.Headers;
using MT.Enterprise.SDK.CronJob;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration;
using Medusa.Service.Ocelot.ApiHost.Core.Enums;
using Medusa.Service.Ocelot.ApiHost.Core.ORM;
using MT.Pretreatment;
using MT.Enterprise.SDK.ApiRequest;
using Medusa.Service.Ocelot.ApiHost.Application.OcelotApi.Dtos;

namespace Medusa.Service.Ocelot.ApiHost.Entrance.Extensions
{
    /// <summary>
    /// 
    /// </summary>
    public class OcelotDataTransDistributor : DelegatingHandler
    {
        private readonly MyDbContext _dbContext;
        private readonly IOcelotApiSerivce _ocelotApiSerivce;
        private readonly IMongoClient _client;
        private readonly ICronJobPublisher _cronJobPublisher;
        private readonly string _ocelotJobUri;
        private readonly string dbName;
        private readonly string mqName;
        private readonly IRabbit _rabbit;
        private readonly IRequestConnector _requestConnector;

        /// <summary>
        /// OcelotDataTransDistributor
        /// </summary>
        /// <param name="thirdProvideSerivce"></param>
        /// <param name="client"></param>
        /// <param name="memoryCache"></param>
        /// <param name="rabbit"></param>
        /// <param name="cronJob"></param>
        /// <param name="requestConnector">requestConnector</param>
        /// <param name="logger">logger</param>
        public OcelotDataTransDistributor(
            IOcelotApiSerivce thirdProvideSerivce,
            IMongoClient client,
            IMemoryCache memoryCache,
            IRabbit rabbit,
            ICronJobPublisher cronJob, IRequestConnector requestConnector)
        {
            _ocelotApiSerivce = thirdProvideSerivce;
            _rabbit = rabbit;
            _cronJobPublisher = cronJob;
            _client = client;
            var appSettings = memoryCache.Get<JObject>("AppSettings");
            dbName = appSettings["CollectSetting"]?["MongoDatabase"].ToString();
            mqName = appSettings["RabbitMQ"]?["DataTransQueue"].ToString();
            _ocelotJobUri = appSettings["OcelotSettings"]?["JobUri"]?.ToString();
            _requestConnector = requestConnector;

            PretreatmentService.Initialize();
        }

        /// <summary>
        /// 重构Send
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            try
            {
                string receivingMode = request.Headers.GetValues("receiving-mode").ToList().FirstOrDefault();
                OcelotExtension.RemoveOcelotHeaderKey(request.Headers);

                if (string.IsNullOrEmpty(receivingMode))
                {
                    throw new Exception("Ocelot文件缺少receiving-mode配置");
                }

                var transPolicy = new TaskDataTransContentDto();

                #region 获取数据交换策略

                // 主动接收
                if (receivingMode == "Active")
                {
                    var requestBody = request.Content.ReadAsStringAsync().Result;
                    var jToken = Newtonsoft.Json.JsonConvert.DeserializeObject<JToken>(requestBody);
                    var dictPost = request.Content.ReadAsAsync<Dictionary<string, object>>().Result;
                    var activeDataTransPolicy = Newtonsoft.Json.JsonConvert.DeserializeObject<TaskDataTransContentDto>(Newtonsoft.Json.JsonConvert.SerializeObject(jToken["IntegrationApiPolicy"]));
                    activeDataTransPolicy.ReceivePolicy.ReceivingMode = receivingMode;

                    transPolicy = activeDataTransPolicy;
                }

                // 被动接收
                if (receivingMode == "Passive")
                {
                    // 获取数据交换策略
                    transPolicy = base.SendAsync(request, cancellationToken).ContinueWith(responseTask =>
                    {
                        var response = responseTask.Result;

                        if (!response.IsSuccessStatusCode)
                        {
                            throw new Exception("数据接收-获取策略：" + response.Content.ReadAsStringAsync().Result);
                        }

                        var policyObj = response.Content.ReadAsAsync<OcelotResponseDto>().Result;
                        if (policyObj.ErrorCode != "0")
                        {
                            throw new Exception("数据接收-获取策略：" + policyObj.ErrorMessage);
                        }

                        var dataTransPolicy = Newtonsoft.Json.JsonConvert.DeserializeObject<TaskDataTransContentDto>(Newtonsoft.Json.JsonConvert.SerializeObject(policyObj.Data));
                        dataTransPolicy.ReceivePolicy.ReceivingMode = receivingMode;

                        return dataTransPolicy;
                    }, cancellationToken).Result;
                }

                #endregion

                #region  获取接收数据

                TaskDataTransContentExtDto dataTransPolicy = transPolicy.ToOurJsonString().ToOurObject<TaskDataTransContentExtDto>();
                dataTransPolicy.PaginationDistribution = false;
                dataTransPolicy.ExecDistribution = false;

                DateTime executionTime = DateTime.Now;

                // 生成主任务号
                if (string.IsNullOrWhiteSpace(dataTransPolicy.ReceivePolicy.Basic.TaskNumber))
                {
                    dataTransPolicy.ReceivePolicy.Basic.TaskNumber = DateTime.Now.ToString("yyyyMMddHHmmssFFFFFFF");
                }

                if (dataTransPolicy.ReceivePolicy.ReceivingMode == "Passive")
                {
                    AddReceiveStartLog(dataTransPolicy, executionTime);

                    // 被动接收数据
                    var passiveData = request.Content.ReadAsStringAsync().Result;

                    string mongoDbSaveErrorMsg = string.Empty;
                    dataTransPolicy.SaveReceiveDataToMongoDbResult = this.SaveData(dataTransPolicy, passiveData, ref mongoDbSaveErrorMsg);
                    dataTransPolicy.MongoDbSaveErrorMsg = mongoDbSaveErrorMsg;
                    dataTransPolicy.ExecDistribution = true;

                }
                else
                {
                    // 是否分页
                    if (dataTransPolicy.ReceivePolicy.Pagination != null && dataTransPolicy.ReceivePolicy.Pagination.IsPagination)
                    {
                        // 生成子任务号
                        if (string.IsNullOrWhiteSpace(dataTransPolicy.ReceivePolicy.Basic.SubTaskNumber))
                        {
                            dataTransPolicy.ReceivePolicy.Basic.SubTaskNumber = $"{dataTransPolicy.ReceivePolicy.Basic.TaskNumber}-001";
                        }
                        else
                        {
                            // 子任务号 + 1
                            var arr = dataTransPolicy.ReceivePolicy.Basic.SubTaskNumber.Split('-');
                            dataTransPolicy.ReceivePolicy.Basic.SubTaskNumber = $"{arr[0]}-{(arr[1].ToOurInt() + 1).ToString().PadLeft(3, '0')}";
                        }

                        #region 分页接收

                        // 获取入参数据
                        request.Content = TransferHttpContent(dataTransPolicy);

                        // 调用接收接口
                        var resultData = base.SendAsync(request, cancellationToken).Result;

                        if (!resultData.IsSuccessStatusCode)
                        {
                            throw new Exception("数据接收：" + resultData.Content.ReadAsStringAsync().Result);
                        }

                        // 接收数据
                        var resultObj = resultData.Content.ReadAsAsync<OcelotResponseDto>().Result;
                        if (resultObj.ErrorCode != "0")
                        {
                            throw new Exception("数据接收：" + resultObj.ErrorMessage);
                        }

                        var resultObjStr = resultObj.Data.ToOurJsonString().Trim();

                        // 数据为空结束分页接收
                        if (resultObj.Data == null || string.IsNullOrEmpty(resultObjStr.Trim()) || resultObjStr.Trim() == "{}"
                        || resultObjStr.Trim() == "[]")
                        {
                            // 统一分发
                            dataTransPolicy.PaginationDistribution = true;
                            dataTransPolicy.ExecDistribution = true;
                        }
                        else
                        {
                            string mongoDbSaveErrorMsg = string.Empty;
                            AddReceiveStartLog(dataTransPolicy, executionTime);

                            dataTransPolicy.ExecDistribution = false;
                            dataTransPolicy.SaveReceiveDataToMongoDbResult = this.SaveData(dataTransPolicy, resultObjStr, ref mongoDbSaveErrorMsg);
                            dataTransPolicy.MongoDbSaveErrorMsg = mongoDbSaveErrorMsg;
                            if (dataTransPolicy.SaveReceiveDataToMongoDbResult)
                            {
                                dataTransPolicy = TransferNextPageDataTransPolicy(dataTransPolicy);
                                this.PushJobAsync(dataTransPolicy);
                            }
                        }

                        #endregion
                    }
                    else
                    {
                        AddReceiveStartLog(dataTransPolicy, executionTime);

                        // 不分页
                        request.Content = this.TransferHttpContent(dataTransPolicy);

                        HttpClient httpClient = new HttpClient();
                        httpClient.Timeout = TimeSpan.FromMinutes(20);

                        var cloneRequest = OcelotExtension.CloneHttpRequestMessageAsync(request).Result;
                        var resultData = httpClient.SendAsync(cloneRequest).Result;

                        if (!resultData.IsSuccessStatusCode)
                        {
                            throw new Exception(resultData.Content.ReadAsStringAsync().Result);
                        }
                        var resultObj = resultData.Content.ReadAsAsync<OcelotResponseDto>().Result;
                        if (resultObj.ErrorCode != "0")
                        {
                            throw new Exception("数据接收：" + resultObj.ErrorMessage);
                        }

                        string mongoDbSaveErrorMsg = string.Empty;
                        string syncBody = Newtonsoft.Json.JsonConvert.SerializeObject(resultObj.Data);

                        dataTransPolicy.SaveReceiveDataToMongoDbResult = this.SaveData(dataTransPolicy, syncBody, ref mongoDbSaveErrorMsg);
                        dataTransPolicy.ExecDistribution = true;
                        dataTransPolicy.MongoDbSaveErrorMsg = mongoDbSaveErrorMsg;
                    }
                }

                #endregion

                #region 数据分发

                var extPolicy = dataTransPolicy;
                if (!string.IsNullOrEmpty(extPolicy.MongoDbSaveErrorMsg))
                {
                    // 详细日志
                    _ocelotApiSerivce.AddLog(extPolicy.ReceivePolicy.Basic, TaskStage.ReceiveEnd, -1, null, null, extPolicy.MongoDbSaveErrorMsg);

                    // 交换状态
                    _ocelotApiSerivce.UpdateExchangeModelStatus(new DtExchangeModelStatus()
                    {
                        ExchangeModelId = extPolicy.ReceivePolicy.Basic.ModelId,
                        CurrentTaskNumber = extPolicy.ReceivePolicy.Basic.TaskNumber,
                        CurrentTaskStage = TaskStage.ReceiveEnd.ToString(),
                        CurrentTaskStatus = (int)Medusa.Service.Ocelot.ApiHost.Core.Enums.TaskStatus.Fail,
                        ErrMsg = extPolicy.MongoDbSaveErrorMsg
                    });
                }
                else
                {
                    if (extPolicy.ExecDistribution)
                    {
                        DateTime execTime = DateTime.Now;
                        if (extPolicy.PaginationDistribution)
                        {
                            var subNumbers = GetSubTaskNumbers(extPolicy);
                            foreach (var item in subNumbers)
                            {
                                extPolicy.ReceivePolicy.Basic.SubTaskNumber = item;

                                // 通知消费端分发
                                this.NoticeMqAsync(extPolicy);
                            }
                        }
                        else
                        {
                            // 通知消费端分发
                            this.NoticeMqAsync(extPolicy);
                        }

                        // 详细日志
                        _ocelotApiSerivce.AddLog(extPolicy.ReceivePolicy.Basic, TaskStage.ReceiveEnd, 1, null, execTime);

                        // 交换状态
                        _ocelotApiSerivce.UpdateExchangeModelStatus(new DtExchangeModelStatus()
                        {
                            ExchangeModelId = extPolicy.ReceivePolicy.Basic.ModelId,
                            CurrentTaskNumber = extPolicy.ReceivePolicy.Basic.TaskNumber,
                            CurrentTaskStage = TaskStage.ReceiveEnd.ToString(),
                            CurrentTaskStatus = (int)Medusa.Service.Ocelot.ApiHost.Core.Enums.TaskStatus.Success
                        });
                    }
                }

                return OcelotExtension.ResponseTaskAsync(new OcelotResponseDto() { ErrorCode = "0" });

                #endregion
            }
            catch (Exception ex)
            {
                Log.Error(ex, "OcelotDataTransDistributor");
                return OcelotExtension.ResponseTaskAsync(new OcelotResponseDto() { ErrorCode = ((int)System.Net.HttpStatusCode.InternalServerError).ToString(), ErrorMessage = ex.Message });
            }
        }

        private bool SaveData(TaskDataTransContentExtDto contentDto, string resultJsonData, ref string errMsg)
        {
            var baseDto = contentDto.ReceivePolicy.Basic;
            var db = _client.GetDatabase(dbName);

            #region MQ

            var tbNameMQ = $"{baseDto.ModelCode}_MQ";
            if (!db.ListCollectionNames().ToList().Any(a => a == tbNameMQ))
            {
                db.CreateCollection(tbNameMQ, null); // 创建表MQ
            }

            var collection = db.GetCollection<CollectStorage>(tbNameMQ);
            var item = collection.Find(f => f.TaskNumber == baseDto.TaskNumber && f.SubTaskNumber == baseDto.SubTaskNumber);
            if (!item.Any())
            {
                collection.InsertOne(new CollectStorage
                {
                    AddDate = DateTime.Now,
                    TaskNumber = baseDto.TaskNumber,
                    SubTaskNumber = baseDto.SubTaskNumber,
                    ReceiveId = baseDto.ReceiveId,
                    Receiver = baseDto.Receiver,
                    ModelId = baseDto.ModelId,
                    ModelCode = baseDto.ModelCode,
                    ModelName = baseDto.ModelName,
                    IsPagination = contentDto.ReceivePolicy.Pagination?.IsPagination,
                    TaskParams = contentDto.ToOurJsonString()
                });
            }

            #endregion


            #region 数据处理

            if (contentDto.ReceivePolicy.DataProcess.Filters.Count > 0 || contentDto.ReceivePolicy.DataProcess.PreProcess)
            {                
                PretreatmentService service = new PretreatmentService();

                // 过滤
                if (contentDto.ReceivePolicy.DataProcess.Filters.Count > 0)
                {
                    _ocelotApiSerivce.AddLog(contentDto.ReceivePolicy.Basic, TaskStage.ReceiveDataFilterStart, (int)Medusa.Service.Ocelot.ApiHost.Core.Enums.TaskStatus.Success, string.Empty, null);
                    List<DataFilter> dataFilters = new List<DataFilter>();
                    contentDto.ReceivePolicy.DataProcess.Filters.ForEach(x =>
                    {
                        dataFilters.Add(new DataFilter()
                        {
                            PropertyName = x.PropertyName,
                            PropertyType = x.PropertyType,
                            Judge = x.Judge,
                            FilterValue = x.FilterValue
                        });
                    });
                    var filterResult = service.RunDataFilter(dataFilters, resultJsonData);
                    if (filterResult.IsSuccess)
                    {
                        _ocelotApiSerivce.AddLog(contentDto.ReceivePolicy.Basic, TaskStage.ReceiveDataFilterEnd, (int)Medusa.Service.Ocelot.ApiHost.Core.Enums.TaskStatus.Success, string.Empty, null);
                        resultJsonData = filterResult.Data.ToString();
                    }
                    else
                    {
                        errMsg = $"过滤异常：{filterResult.Message}";
                        return false;
                    }
                }

                // 预处理
                if (contentDto.ReceivePolicy.DataProcess.PreProcess)
                {
                    ResponResult processResult = null;
                    _ocelotApiSerivce.AddLog(contentDto.ReceivePolicy.Basic, TaskStage.ReceivePreHandleStart, (int)Medusa.Service.Ocelot.ApiHost.Core.Enums.TaskStatus.Success, string.Empty, null);
                    if (contentDto.ReceivePolicy.DataProcess.ProcessMethod == "C#")
                    {
                        processResult = service.RunCsharpCode(contentDto.ReceivePolicy.DataProcess.Code, resultJsonData);
                    }
                    else
                    {
                        var res = _requestConnector.PostAsync<ResponResult>(contentDto.ReceivePolicy.DataProcess.API, resultJsonData).Result;
                        processResult = new ResponResult()
                        {
                            IsSuccess = res.IsSuccessStatusCode,
                            Data = res.ResouceContent,
                            Message = res.ErrorMessage
                        };
                    }
                    if (processResult.IsSuccess)
                    {
                        _ocelotApiSerivce.AddLog(contentDto.ReceivePolicy.Basic, TaskStage.ReceivePreHandleEnd, (int)Medusa.Service.Ocelot.ApiHost.Core.Enums.TaskStatus.Success, string.Empty, null);
                        resultJsonData = processResult.Data.ToString();
                    }
                    else
                    {
                        errMsg = $"预处理异常：{processResult.Message}";
                        return false;
                    }
                }
            }


            #endregion

            #region Data

            var tbNameData = $"{baseDto.ModelCode}_Data";
            if (!db.ListCollectionNames().ToList().Any(a => a == tbNameData))
            {
                db.CreateCollection(tbNameData, null); // 创建表Data
            }

            var dataCollection = db.GetCollection<Dictionary<string, string>>(tbNameData);
            dataCollection.DeleteMany(r => r["TaskNumber"] == baseDto.TaskNumber && r["SubTaskNumber"] == baseDto.SubTaskNumber);

            List<Dictionary<string, string>> liDictData = Newtonsoft.Json.JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(resultJsonData);
            if (liDictData.Any())
            {
                liDictData.ForEach(r =>
                {
                    r["TaskNumber"] = baseDto.TaskNumber;
                    r["SubTaskNumber"] = baseDto.SubTaskNumber;
                });
                dataCollection.InsertMany(liDictData);
            }

            #endregion

            return true;
        }

        /// <summary>
        /// 获取所有子任务号
        /// </summary>
        /// <param name="contentDto"></param>
        /// <returns></returns>
        private List<string> GetSubTaskNumbers(TaskDataTransContentDto contentDto)
        {
            var baseDto = contentDto.ReceivePolicy.Basic;
            var tbName = $"{baseDto.ModelCode}_MQ";
            var db = _client.GetDatabase(dbName);
            var collection = db.GetCollection<CollectStorage>(tbName);
            var items = collection.Find(f => f.TaskNumber == baseDto.TaskNumber).ToList();
            return items.Where(s => !string.IsNullOrWhiteSpace(s.SubTaskNumber)).Select(s => s.SubTaskNumber).ToList(); ;
        }

        private TaskDataTransContentExtDto TransferNextPageDataTransPolicy(TaskDataTransContentExtDto dataTransPolicy)
        {
            dataTransPolicy.ReceivePolicy.InputParamBody = TransferInputBody(dataTransPolicy, true);

            return dataTransPolicy;
        }

        /// <summary>
        /// Body Content
        /// </summary>
        /// <param name="dataTransPolicy">交换策略</param>
        /// <param name="nextPage">是否下一页</param>
        /// <returns></returns>
        private JToken TransferInputBody(TaskDataTransContentDto dataTransPolicy, bool nextPage = false)
        {
            JToken inputBody = Newtonsoft.Json.JsonConvert.DeserializeObject<JToken>(Newtonsoft.Json.JsonConvert.SerializeObject(dataTransPolicy.ReceivePolicy.InputParamBody));
            if (nextPage)
            {
                // 只更新PageIndex
                dataTransPolicy.ReceivePolicy.Attributes.ForEach(r =>
                {
                    switch (r.AttributeType.ToOurEnum<AttributeType>())
                    {
                        case AttributeType.PageIndex:
                            inputBody[r.ParamName] = inputBody[r.ParamName].ToOurString().ToOurInt() + 1;
                            break;
                    }
                });
            }
            else
            {
                dataTransPolicy.ReceivePolicy.Attributes.ForEach(r =>
                {
                    switch (r.AttributeType.ToOurEnum<AttributeType>())
                    {
                        case AttributeType.ShortDateIncrement:
                            inputBody[r.ParamName] = DateTime.Now.ToString("yyyy-MM-dd");
                            break;
                    }
                });
            }

            return inputBody;
        }

        /// <summary>
        /// Body Content
        /// </summary>
        /// <param name="dataTransPolicy">交换策略</param>
        /// <param name="nextPage">是否下一页</param>
        /// <returns></returns>
        private JToken TransferInputBody(TaskDataTransContentExtDto dataTransPolicy, bool nextPage = false)
        {
            JToken inputBody = Newtonsoft.Json.JsonConvert.DeserializeObject<JToken>(Newtonsoft.Json.JsonConvert.SerializeObject(dataTransPolicy.ReceivePolicy.InputParamBody));
            if (nextPage)
            {
                // 只更新PageIndex
                dataTransPolicy.ReceivePolicy.Attributes.ForEach(r =>
                {
                    switch (r.AttributeType.ToOurEnum<AttributeType>())
                    {
                        case AttributeType.PageIndex:
                            inputBody[r.ParamName] = inputBody[r.ParamName].ToOurString().ToOurInt() + 1;
                            break;
                    }
                });
            }
            else
            {
                dataTransPolicy.ReceivePolicy.Attributes.ForEach(r =>
                {
                    switch (r.AttributeType.ToOurEnum<AttributeType>())
                    {
                        case AttributeType.ShortDateIncrement:
                            inputBody[r.ParamName] = DateTime.Now.ToString("yyyy-MM-dd");
                            break;
                    }
                });
            }

            return inputBody;
        }

        /// <summary>
        /// Body Content
        /// </summary>
        /// <param name="dataTransPolicy">交换策略</param>
        /// <param name="nextPage">是否下一页</param>
        /// <returns></returns>
        private HttpContent TransferHttpContent(TaskDataTransContentDto dataTransPolicy, bool nextPage = false)
        {
            JToken inputBody = TransferInputBody(dataTransPolicy, nextPage);

            var inputBodyJson = Newtonsoft.Json.JsonConvert.SerializeObject(inputBody);
            HttpContent httpContent = new StringContent(inputBodyJson, System.Text.Encoding.UTF8);
            httpContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            return httpContent;
        }

        /// <summary>
        /// 推送Mq
        /// </summary>
        /// <param name="data"></param>
        private Task NoticeMqAsync(TaskDataTransContentDto data)
        {
            var distributionContentDto = Newtonsoft.Json.JsonConvert.DeserializeObject<TaskDataTransContentDto>(Newtonsoft.Json.JsonConvert.SerializeObject(data));

            // 拆分分发器，一个分发器一个MQ
            data.DistributionPolicies.ForEach(r =>
            {
                distributionContentDto.DistributionPolicies = new List<TaskDistributionPolicyDto>()
                {
                    r
                };

                _rabbit.PublishDirect(Newtonsoft.Json.JsonConvert.SerializeObject(distributionContentDto), mqName);
            });


            return Task.CompletedTask;
        }

        /// <summary>
        /// 推送Job
        /// </summary>
        /// <param name="dataTransPolicy">策略数据</param>
        private Task PushJobAsync(TaskDataTransContentDto dataTransPolicy)
        {
            var job = new Job()
            {
                JobKeyWords = dataTransPolicy.ReceivePolicy.Basic.ModelName,
                Name = $"Integration-Job-Pagination-{dataTransPolicy.ReceivePolicy.Basic.ModelCode}",
                Data = new JobData()
                {
                    Uri = $"{_ocelotJobUri}/ExRece/{dataTransPolicy.ReceivePolicy.Basic.ReceiveId.ToOurString().ToUpper().Replace("-", string.Empty)}",
                    Method = "Post",
                    Content = new Dictionary<string, object>() { { "IntegrationApiPolicy", dataTransPolicy } }
                }
            };

            return _cronJobPublisher.PublishJobAsync(job);
        }

        private void AddReceiveStartLog(TaskDataTransContentDto dataTransPolicy, DateTime executionTime)
        {
            // 详细日志
            _ocelotApiSerivce.AddLog(dataTransPolicy.ReceivePolicy.Basic, TaskStage.ReceiveStart, 1, Newtonsoft.Json.JsonConvert.SerializeObject(dataTransPolicy), executionTime);

            // 交换状态
            _ocelotApiSerivce.UpdateExchangeModelStatus(new DtExchangeModelStatus()
            {
                ExchangeModelId = dataTransPolicy.ReceivePolicy.Basic.ModelId,
                CurrentTaskNumber = dataTransPolicy.ReceivePolicy.Basic.TaskNumber,
                CurrentTaskStage = TaskStage.ReceiveStart.ToString(),
                CurrentTaskStatus = (int)Medusa.Service.Ocelot.ApiHost.Core.Enums.TaskStatus.Success
            });
        }
    }
}
