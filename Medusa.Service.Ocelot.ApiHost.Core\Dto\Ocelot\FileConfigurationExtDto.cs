﻿using System.Collections.Generic;
using Ocelot.Configuration.File;

namespace Medusa.Service.Ocelot.ApiHost.Core.Dto.Ocelot
{
    /// <summary>
    /// 
    /// </summary>
    public class FileConfigurationExtDto
    {
        /// <summary>
        /// 
        /// </summary>
        public List<FileRouteExtDto> Routes { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<FileDynamicRoute> DynamicRoutes { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<FileAggregateRoute> Aggregates { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public FileGlobalConfiguration GlobalConfiguration { get; set; }
    }
}
