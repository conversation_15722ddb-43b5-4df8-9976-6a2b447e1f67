﻿using System;
using System.Collections.Generic;
using System.Text;
using Ocelot.Configuration;

namespace Medusa.Service.Ocelot.ApiHost.Core.Dtos
{
    /// <summary>
    /// Ocelot Route
    /// </summary>
    [Serializable]
    public class OcelotRouteDto
    {
        /// <summary>
        /// 下游格式
        /// </summary>
        public string DownstreamScheme { get; set; }

        /// <summary>
        /// 下游路径
        /// </summary>
        public string DownstreamPathTemplate { get; set; }

        /// <summary>
        /// 下游方式
        /// </summary>
        public string DownstreamHttpMethod { get; set; }

        /// <summary>
        /// 下游地址及端口
        /// </summary>
        public List<OcelotHostAndPortDto> DownstreamHostAndPorts { get; set; }

        /// <summary>
        /// 上游路径
        /// </summary>
        public string UpstreamPathTemplate { get; set; }

        /// <summary>
        /// 上游请求方式
        /// </summary>
        public List<string> UpstreamHttpMethod { get; set; }

        /// <summary>
        /// 上游传入下游的头部信息
        /// </summary>
        public Dictionary<string, string> UpstreamHeaderTransform { get; set; }

        /// <summary>
        /// 委托连接器
        /// </summary>
        public List<string> DelegatingHandlers { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public AuthenticationOptions AuthenticationOptions { get; set; }

        /// <summary>
        /// ApiId
        /// </summary>
        public Guid ApiId { get; set; }
    }
}
