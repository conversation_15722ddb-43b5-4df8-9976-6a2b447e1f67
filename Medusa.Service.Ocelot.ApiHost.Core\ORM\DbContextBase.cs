using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Core.ORM
{
    /// <summary>
    /// 数据库上下文基类
    /// </summary>
    public abstract class DbContextBase
    {
        private readonly IDbContext _dbContext;

        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="dbContext">数据库上下文</param>
        protected DbContextBase(IDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// _dbContext
        /// </summary>
        public IDbContext DbContext => _dbContext;

        /// <summary>
        /// Integration
        /// </summary>
        public virtual MultipleClient Integration
        {
            get { return _dbContext["Integration"]; }
        }
    }
}