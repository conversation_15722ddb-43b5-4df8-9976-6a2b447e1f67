﻿using System;
using System.Collections.Generic;
using System.Text;
using Newtonsoft.Json;

namespace Medusa.Service.Ocelot.ApiHost.Application.OcelotApi.Dtos
{
    /// <summary>
    /// 
    /// </summary>
    public class IdentityServer4TokenDto
    {
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "access_token")]
        public string AccessToken { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "expires_in")]
        public int ExpiresIn { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(PropertyName = "token_type")]
        public string TokenType { get; set; }
    }
}
