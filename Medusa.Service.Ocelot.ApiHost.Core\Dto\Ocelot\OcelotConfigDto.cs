﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Core.Dtos
{
    /// <summary>
    /// Ocelot Config
    /// </summary>
    [Serializable]
    public class OcelotConfigDto
    {
        /// <summary>
        /// Ocelot Config
        /// </summary>
        public OcelotConfigDto()
        {
            Routes = new List<OcelotRouteDto>();
        }

        /// <summary>
        /// 
        /// </summary>
        public List<OcelotRouteDto> Routes { get; set; }
    }
}
