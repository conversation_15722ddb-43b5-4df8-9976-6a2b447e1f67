using System.Collections.Generic;
using Medusa.Service.Ocelot.ApiHost.Core;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Medusa.Service.Ocelot.ApiHost.Entrance.Development
{
    /// <summary>
    /// BaseCustomHeaderFilter
    /// </summary>
    public class CustomHeaderFilter : IOperationFilter
    {
        /// <summary>
        /// Apply
        /// </summary>
        /// <param name="operation">operation</param>
        /// <param name="context">上下文</param>
        public  void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            operation.Parameters ??= new List<OpenApiParameter>();

            operation.Parameters.Add(new OpenApiParameter()
            {
                Name = AppConstants.HeaderUser,
                In = ParameterLocation.Header,
                Description = "当前用户 json&base64 字符串\n默认值：{\"id\":\"E8575564-17E8-4D9A-8AB3-E055C366DC62\",\"name\":\"管理员\",\"account\":\"admin\",\"language\":\"zh\"}",
                Schema = new OpenApiSchema { Type = "string", Default = new OpenApiString("eyJpZCI6IkU4NTc1NTY0LTE3RTgtNEQ5QS04QUIzLUUwNTVDMzY2REM2MiIsIm5hbWUiOiLnrqHnkIblkZgiLCJhY2NvdW50IjoiYWRtaW4iLCJsYW5ndWFnZSI6InpoIn0=") },
            });
        }
    }
}