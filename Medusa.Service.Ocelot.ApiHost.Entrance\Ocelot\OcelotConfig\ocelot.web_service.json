{"Routes": [{"DownstreamScheme": "http", "DownstreamPathTemplate": "/Common_Service.asmx/GetAllApprovalHistory", "DownstreamHttpMethod": "Post", "DownstreamHostAndPorts": [{"Host": "*************", "Port": 93}], "UpstreamPathTemplate": "/Pcc/Process-Record", "UpstreamHttpMethod": ["Post"], "UpstreamHeaderTransform": {"api-token": "EduhhyzQG49YAHTCgzd6akvWYxzWXASu7XjAZgXBCcza9pN9D4DPwNqqIHIEN19+KUmdvVdOcdgpRjlToVA+8Q==", "request-module": "Mapping", "down-securitycert-model": "None", "down-securitycert-data": null}, "DelegatingHandlers": ["OcelotWebDistributor"], "AuthenticationOptions": {"AllowedScopes": ["AdminScope"], "AuthenticationProviderKey": "ApiIds4"}, "ApiId": "78526a2f-40f4-4f9a-9640-577e28dac447"}]}