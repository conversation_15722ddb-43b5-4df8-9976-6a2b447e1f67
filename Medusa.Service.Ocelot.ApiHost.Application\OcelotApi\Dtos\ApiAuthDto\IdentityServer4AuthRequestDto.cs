﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Ocelot.ApiHost.Application.OcelotApi.Dtos
{
    /// <summary>
    /// 
    /// </summary>
    public class IdentityServer4AuthRequestDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string Token<PERSON>pi { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ClientSecret { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ServiceCode { get; set; }
    }
}
