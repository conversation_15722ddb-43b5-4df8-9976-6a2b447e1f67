﻿using System.Collections.Generic;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration;

namespace Medusa.Service.Ocelot.ApiHost.Application.OcelotApi.Dtos
{
    /// <summary>
    /// 
    /// </summary>
    public class ParamSignMd5Dto
    {
        /// <summary>
        /// 
        /// </summary>
        public string SignKey { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string SignSecret { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<ServiceAuthParam> AuthParams { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string RequestMethod { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DataFormat { get; set; }
    }
}
