using System.Collections.Generic;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Application.OcelotApi.Dtos
{
    /// <summary>
    /// Api组合项扩展实体
    /// </summary>
    public class ApiGroupItemDto : ApiGroupItem
    {
        /// <summary>
        /// Api名称
        /// </summary>
        [EntityColumn(IsIgnore = true)]
        public string ApiName { get; set; }

        /// <summary>
        /// Api地址
        /// </summary>
        [EntityColumn(IsIgnore = true)]
        public string RequestPath { get; set; }

        /// <summary>
        /// Api请求参数映射
        /// </summary>
        [EntityColumn(IsIgnore = true)]
        public List<ApiGroupItemParamMapping> ParamMappings { get; set; }
    }
}