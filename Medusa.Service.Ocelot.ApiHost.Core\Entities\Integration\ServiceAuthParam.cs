using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration
{
    /// <summary>
    /// 服务表
    /// </summary>
    [EntityTable("ServiceAuthParam", "服务认证参数表")]
    public class ServiceAuthParam
    {
        /// <summary>
        /// 参数Id
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "参数Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ParamId { get; set; }

        /// <summary>
        /// 参数名称
        /// </summary>
        [EntityColumn(ColumnDescription = "参数名称")]
        public string ParamName { get; set; }

        /// <summary>
        /// 参数位置
        /// </summary>
        [EntityColumn(ColumnDescription = "参数位置")]
        public string ParamLocation { get; set; }

        /// <summary>
        /// 参数类型
        /// </summary>
        [EntityColumn(ColumnDescription = "参数类型")]
        public string DataType { get; set; }

        /// <summary>
        /// 是否必填
        /// </summary>
        [EntityColumn(ColumnDescription = "是否必填")]
        public bool? Required { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [EntityColumn(ColumnDescription = "描述")]
        public string Description { get; set; }

        /// <summary>
        /// 映射类型
        /// </summary>
        [EntityColumn(ColumnDescription = "映射类型")]
        public string MappingType { get; set; }

        /// <summary>
        /// 映射值
        /// </summary>
        [EntityColumn(ColumnDescription = "映射值")]
        public string MappingValue { get; set; }

        /// <summary>
        /// 排序位置
        /// </summary>
        [EntityColumn(ColumnDescription = "排序位置")]
        public int? Sequencing { get; set; }

        /// <summary>
        /// 父级参数Id
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, ColumnDescription = "父级参数Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? UpperParamId { get; set; }

        /// <summary>
        /// 服务Id
        /// </summary>
        [EntityColumn(ColumnDescription = "服务Id", ColumnDataType = "uniqueidentifier,varchar(36)")]
        public Guid? ServiceId { get; set; }
    }
}
