﻿using System.Collections.Generic;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Ocelot.ApiHost.Application.OcelotApi.Dtos
{
    /// <summary>
    /// ApiResultDesignMappingDto
    /// </summary>
    public class ApiResultDesignMappingDto : ApiResultMapping
    {
        /// <summary>
        /// 返回结果-映射路径
        /// </summary>
        [EntityColumn(IsIgnore = true)]
        public List<string> MappingSchemaPath { get; set; }
    }
}
