{"Nacos": {"ServerAddresses": ["http://*************:8848"], "DefaultTimeOut": 15000, "Namespace": "shui<PERSON>", "ListenInterval": 1000, "GroupId": "integrationcenter", "DataId": "medusa.service.integration.ocelot.appsettings.json", "LanguageGroupId": "i18n", "LanguageIds": ["en.json", "zh.json"], "ServiceName": "OcelotApiGateway"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Persistence": {"Boost": {"DbType": "Mysql", "ConnectionString": "server=*************;Port=3306;Database=boost_standard;uid=bpm;Pwd=****************;"}, "Integration": {"DbType": "Mysql", "ConnectionString": "server=*************;Port=3306;Database=interfacecenter_dev;uid=bpm;Pwd=****************;"}}, "CollectSetting": {"MongoConnectionString": "mongodb://localhost:27017", "MongoDatabase": "DataExchange"}, "OcelotSettings": {"JobUri": "/integration-ocelot"}, "ApiRequest": {"Host": "*************:6379", "DB": 8, "Password": "7Qx0YFWyUDsbjzBv", "Timeout": "600", "IsHttps": false}, "RabbitMQ": {"HostName": "*************", "Port": "5672", "UserName": "guest", "Password": "guest", "DataTransQueue": "integration:data-trans"}, "CronJob": {"Host": "http://*************", "Port": "32004", "Uri": "/job", "BasicUserName": "bpm", "BasicPassword": "rPFwdOQHXnl5mzCW", "DefaultTimeOut": 180000}, "OcelotConfigFolder": "./OcelotConfig", "Ids4Setting": {"IP": "*************", "Port": 32065, "IdentityScheme": "Bearer"}, "NacosOcelotData": {"DataId": "medusa.service.integration.ocelot.routes.json", "Group": "integrationcenter"}, "Ids4Mapping": {"Beisen": {"client_id": "app_key", "client_secret": "app_secret"}}}