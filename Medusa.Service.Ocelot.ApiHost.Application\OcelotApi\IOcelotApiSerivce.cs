﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using Medusa.Service.Ocelot.ApiHost.Application.Dtos;
using Medusa.Service.Ocelot.ApiHost.Application.OcelotApi.Dtos;
using Medusa.Service.Ocelot.ApiHost.Core.Dtos;
using Medusa.Service.Ocelot.ApiHost.Core.Entities.Integration;
using Medusa.Service.Ocelot.ApiHost.Core.Enums;
using Newtonsoft.Json.Linq;

namespace Medusa.Service.Ocelot.ApiHost.Application.OcelotApi
{
    /// <summary>
    /// 
    /// </summary>
    public interface IOcelotApiSerivce
    {
        /// <summary>
        /// 获取三方接口
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>返回接口中信息</returns>
        List<InterfaceInfoDto> GetThirdInterfaces(InterfaceInfoQueryDto queryDto);

        /// <summary>
        /// 获取组合接口
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>返回接口中信息</returns>
        List<InterfaceInfoDto> GetThirdGroupInterfaces(InterfaceInfoQueryDto queryDto);

        /// <summary>
        /// 获取三方接口结构
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>接口结构</returns>
        dynamic GetThirdInterfaceSchema(InterfaceSchemaQueryDto queryDto);

        /// <summary>
        /// 获取组合接口
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>接口结构</returns>
        dynamic GetThirdGroupInterfacesSchema(InterfaceSchemaQueryDto queryDto);

        /// <summary>
        /// 获取Mock信息
        /// </summary>
        /// <param name="api">api对象</param>
        /// <returns>返回Mock信息</returns>
        OcelotResponseDto GetInterfaceMockInfo(Apis api);

        /// <summary>
        /// sql exce
        /// </summary>
        /// <param name="api">api对象</param>
        /// <param name="postJsonToken">参数</param>
        /// <returns>Database信息</returns>
        OcelotResponseDto ExecSqlForDb(Apis api, JToken postJsonToken);

        /// <summary>
        /// 结果映射
        /// </summary>
        /// <param name="responseValue"></param>
        /// <param name="api"></param>
        /// <returns></returns>
        OcelotResponseDto ResponseMapping(string responseValue, Apis api);

        /// <summary>
        /// 获取API
        /// </summary>
        /// <param name="apiToken">apiToken</param>
        /// <returns>API</returns>
        Apis GetApis(string apiToken);

        /// <summary>
        /// 更新模型运行状态
        /// </summary>
        /// <param name="statusEntity"></param>
        void UpdateExchangeModelStatus(DtExchangeModelStatus statusEntity);

        /// <summary>
        /// 添加日志
        /// </summary>
        /// <param name="receiveBasic">接收基础数据</param>
        /// <param name="taskStage"></param>
        /// <param name="taskStatus">1成功，-1失败</param>
        /// <param name="dataMsg"></param>
        /// <param name="executionTime"></param>
        /// <param name="errMsg"></param>
        void AddLog(TaskReceiveBasicDto receiveBasic, TaskStage taskStage, int taskStatus, string dataMsg, DateTime? executionTime, string errMsg = "");

        /// <summary>
        /// 入参映射
        /// </summary>
        /// <param name="request"></param>
        /// <param name="api"></param>
        /// <param name="postJToken"></param>
        /// <param name="serviceType"></param>
        (Dictionary<string, object>, ServiceApis) InputParameterMapping(HttpRequestMessage request, Apis api, JToken postJToken, BackendServiceType serviceType);

        /// <summary>
        /// 获取Api组合
        /// </summary>
        /// <param name="apiToken">apiToken</param>
        /// <returns></returns>
        ApiGroupDto GetApiGroup(string apiToken);

        /// <summary>
        /// 获取Api请求参数集合
        /// </summary>
        /// <param name="apiBaseId">apiBaseId</param>
        /// <returns>请求参数集合</returns>
        List<ApiRequestBodySchema> GetApiRequestBodySchemas(Guid? apiBaseId);

        /// <summary>
        /// 获取Api请求结果集
        /// </summary>
        /// <param name="apiBaseId">apiBaseId</param>
        /// <returns>请求结果集</returns>
        List<ApiResponseSchemaDto> GetApiResponseSchemas(Guid? apiBaseId);

        /// <summary>
        /// 下游API认证
        /// </summary>
        /// <param name="api"></param>
        /// <param name="downstreamApi"></param>
        /// <param name="request"></param>
        void DownstreamApiAuth(Apis api, ServiceApis downstreamApi, HttpRequestMessage request);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="api"></param>
        /// <param name="responseValue"></param>
        void ExecCustomSuccess(Apis api, string responseValue);

        /// <summary>
        /// Http/Https 请求信息处理
        /// </summary>
        /// <param name="api">Api对象</param>
        /// <param name="requestMsg">请求信息</param>
        /// <param name="processAction">处理动作</param>
        /// <returns></returns>
        Task<HttpRequestMessage> HttpRequestMsgProcessAsync(Apis api, HttpRequestMessage requestMsg, Dictionary<string, string> processAction);
    }
}
