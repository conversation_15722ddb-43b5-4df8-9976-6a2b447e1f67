﻿using System.Threading.Tasks;
using Medusa.Service.Ocelot.ApiHost.Core.Dto;
using Microsoft.AspNetCore.Http;
using MT.Enterprise.Core.I18n;
using MT.Enterprise.Utils;
using MT.Enterprise.Utils.Extensions;

namespace Medusa.Service.Ocelot.ApiHost.Core.Middlewares
{
    /// <summary>
    /// 用户状态中间件
    /// </summary>
    public class UserStateMiddleware
    {
        readonly RequestDelegate _next;

        /// <summary>
        /// Initializes a new instance of the <see cref="UserStateMiddleware"/> class.
        /// 构造
        /// </summary>
        /// <param name="next">下一个中间件</param>
        public UserStateMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        /// <summary>
        /// 调用
        /// </summary>
        /// <param name="context">上下文</param>
        /// <returns>task</returns>
        public async Task Invoke(HttpContext context)
        {
            await HandlerAsync(context);
            await _next(context);
        }

        static Task HandlerAsync(HttpContext context)
        {
            var headerUser = context.Request.Headers[AppConstants.HeaderUser];
            if (headerUser.Count > 0)
            {
                var jsonStr = Crypto.Base64Decrypt(headerUser.ToString());
                var user = jsonStr.ToOurObject<HeaderUser>();
                if (!string.IsNullOrEmpty(user.Language) && user.Language != I18nManager.Lang)
                {
                    if (user.Language == "zh")
                    {
                        user.Language = "zh-CN";
                    }
                    else if (user.Language == "en")
                    {
                        user.Language = "en-US";
                    }

                    I18nManager.Lang = user.Language;
                }

                AppConstants.CurrentUser.Value = user;
            }

            return Task.CompletedTask;
        }
    }
}
